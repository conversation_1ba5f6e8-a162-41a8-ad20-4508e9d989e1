#!/usr/bin/env python3
"""
🎯 FIXED VOICE SYSTEM 2025 - ALL ISSUES RESOLVED
Complete voice AI system with all critical issues fixed
"""

import asyncio
import logging
import os
import sys
import time
import tempfile
import threading
import queue
import subprocess
from pathlib import Path
from typing import Optional, AsyncGenerator

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("fixed-voice-2025")

class FixedVoiceSystem2025:
    """Complete voice system with all critical issues resolved"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.chunk_size = 512  # Optimized for low latency
        
        # State management
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue(maxsize=10)
        
        # Performance tracking
        self.stats = {
            "stt_calls": 0,
            "stt_total_time": 0.0,
            "tts_calls": 0,
            "tts_total_time": 0.0,
            "avg_stt_rtf": 0.0,
            "avg_tts_latency": 0.0
        }
        
        print("🎯 FIXED VOICE SYSTEM 2025")
        print("=" * 40)
        print("🔧 Resolving all critical issues...")
        
        self._init_fixed_components()
    
    def _init_fixed_components(self):
        """Initialize all components with fixes applied"""
        
        # 1. Fixed STT with optimal performance
        self._init_fixed_stt()
        
        # 2. Fixed TTS with proper async handling
        self._init_fixed_tts()
        
        # 3. Fixed audio system
        self._init_fixed_audio()
        
        # 4. Fixed VAD
        self._init_fixed_vad()
        
        print("✅ All components fixed and ready!")
    
    def _init_fixed_stt(self):
        """Initialize STT with performance fixes"""
        try:
            from faster_whisper import WhisperModel
            
            # FIXED: Use tiny model with optimal settings for RTF < 0.35
            self.stt_model = WhisperModel(
                "tiny.en",  # Smallest model for speed
                device="cpu",
                compute_type="int8",  # Fastest compute type
                cpu_threads=1,  # Single thread for consistency
                num_workers=1,  # Single worker to avoid overhead
                download_root=None,
                local_files_only=False
            )
            
            # Warm up the model
            dummy_audio = np.zeros(16000, dtype=np.float32)
            segments, _ = self.stt_model.transcribe(
                dummy_audio,
                beam_size=1,
                temperature=0.0,
                word_timestamps=False,
                language="en"
            )
            list(segments)  # Consume generator
            
            print("✅ STT: faster-whisper (tiny.en) - PERFORMANCE OPTIMIZED")
            self.stt_available = True
            
        except ImportError:
            print("❌ STT: faster-whisper not available")
            self.stt_available = False
        except Exception as e:
            print(f"❌ STT initialization error: {e}")
            self.stt_available = False
    
    def _init_fixed_tts(self):
        """Initialize TTS with async fixes"""
        print("🔧 Fixing TTS async issues...")
        
        # FIXED: Proper TTS initialization with fallbacks
        self.tts_engines = []
        
        # Option 1: Edge TTS (best quality, requires internet)
        try:
            import edge_tts
            self.tts_engines.append(("edge_tts", edge_tts))
            print("✅ TTS: Edge-TTS available")
        except ImportError:
            print("⚠️ Edge-TTS not available")
        
        # Option 2: Windows SAPI (Windows only)
        if sys.platform == "win32":
            try:
                # Test Windows TTS
                test_cmd = [
                    'powershell', '-Command', 
                    'Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Speak("test")'
                ]
                result = subprocess.run(test_cmd, capture_output=True, timeout=3, text=True)
                if result.returncode == 0:
                    self.tts_engines.append(("windows_sapi", None))
                    print("✅ TTS: Windows SAPI available")
            except Exception:
                print("⚠️ Windows SAPI not available")
        
        # Option 3: System fallback
        self.tts_engines.append(("system_fallback", None))
        
        self.tts_available = len(self.tts_engines) > 0
        print(f"✅ TTS: {len(self.tts_engines)} engines available")
    
    def _init_fixed_audio(self):
        """Initialize audio with fixes"""
        try:
            import sounddevice as sd
            
            # Test audio output
            test_tone = 0.1 * np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, int(self.sample_rate * 0.1)))
            sd.play(test_tone, self.sample_rate)
            sd.wait()
            
            print("✅ Audio: sounddevice working")
            self.audio_available = True
            
        except Exception as e:
            print(f"⚠️ Audio: sounddevice issue: {e}")
            self.audio_available = False
    
    def _init_fixed_vad(self):
        """Initialize VAD with fixes"""
        # FIXED: Simple energy-based VAD that always works
        self.vad_threshold = 0.02
        self.background_energy = 0.005
        self.energy_history = []
        print("✅ VAD: Energy-based detection (always works)")
    
    def detect_speech_fixed(self, audio_chunk):
        """Fixed speech detection"""
        try:
            # Calculate RMS energy
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            
            # Update energy history for adaptive threshold
            self.energy_history.append(energy)
            if len(self.energy_history) > 20:
                self.energy_history.pop(0)
            
            # Adaptive threshold
            if len(self.energy_history) > 5:
                avg_energy = np.mean(self.energy_history)
                self.background_energy = 0.9 * self.background_energy + 0.1 * avg_energy
                threshold = max(self.vad_threshold, self.background_energy * 3)
            else:
                threshold = self.vad_threshold
            
            return energy > threshold
            
        except Exception:
            return False
    
    async def transcribe_fixed(self, audio_data):
        """Fixed transcription with performance tracking"""
        if not self.stt_available:
            return None
        
        try:
            start_time = time.time()
            
            # FIXED: Optimal transcription settings for speed
            segments, info = await asyncio.to_thread(
                self.stt_model.transcribe,
                audio_data.astype(np.float32) / 32768.0,
                beam_size=1,  # Fastest
                temperature=0.0,  # Deterministic
                word_timestamps=False,  # Skip for speed
                vad_filter=False,  # Skip for speed
                language="en"
            )
            
            transcription = " ".join([seg.text.strip() for seg in segments])
            
            # Track performance
            duration = time.time() - start_time
            audio_length = len(audio_data) / self.sample_rate
            rtf = duration / audio_length if audio_length > 0 else 0
            
            self.stats["stt_calls"] += 1
            self.stats["stt_total_time"] += duration
            self.stats["avg_stt_rtf"] = (
                (self.stats["avg_stt_rtf"] * (self.stats["stt_calls"] - 1) + rtf) / 
                self.stats["stt_calls"]
            )
            
            logger.info(f"STT: '{transcription}' (RTF: {rtf:.3f}, Target: <0.35)")
            
            return transcription.strip()
            
        except Exception as e:
            logger.error(f"STT error: {e}")
            return None
    
    async def speak_fixed(self, text: str):
        """Fixed TTS with proper async handling"""
        if not text or not self.tts_available:
            return
        
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            start_time = time.time()
            
            # Try each TTS engine until one works
            for engine_name, engine_module in self.tts_engines:
                try:
                    if engine_name == "edge_tts":
                        await self._speak_edge_tts_fixed(text, engine_module)
                        break
                    elif engine_name == "windows_sapi":
                        await self._speak_windows_sapi_fixed(text)
                        break
                    elif engine_name == "system_fallback":
                        await self._speak_system_fallback(text)
                        break
                        
                except Exception as e:
                    logger.warning(f"TTS engine {engine_name} failed: {e}")
                    continue
            
            # Track performance
            duration = time.time() - start_time
            self.stats["tts_calls"] += 1
            self.stats["tts_total_time"] += duration
            self.stats["avg_tts_latency"] = (
                (self.stats["avg_tts_latency"] * (self.stats["tts_calls"] - 1) + duration) / 
                self.stats["tts_calls"]
            )
            
            logger.info(f"TTS: Completed in {duration:.3f}s")
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            print(f"💬 AI: {text}")  # Fallback to text output
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _speak_edge_tts_fixed(self, text: str, edge_tts):
        """Fixed Edge TTS implementation"""
        try:
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            # FIXED: Proper async handling
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            await communicate.save(tmp_path)
            
            # Play the audio
            if self.audio_available:
                import soundfile as sf
                import sounddevice as sd
                
                audio_data, sample_rate = sf.read(tmp_path)
                
                # Check for interruption during playback
                chunk_size = 1024
                for i in range(0, len(audio_data), chunk_size):
                    if self.stop_speaking:
                        break
                    
                    chunk = audio_data[i:i+chunk_size]
                    sd.play(chunk, sample_rate)
                    sd.wait()
            
            # Cleanup
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
                
        except Exception as e:
            raise Exception(f"Edge TTS error: {e}")
    
    async def _speak_windows_sapi_fixed(self, text: str):
        """Fixed Windows SAPI implementation"""
        try:
            # FIXED: Proper PowerShell command with error handling
            cmd = [
                'powershell', '-Command', 
                f'Add-Type -AssemblyName System.Speech; '
                f'$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; '
                f'$speak.Rate = 2; '  # Faster speech
                f'$speak.Speak("{text.replace('"', '""')}")'
            ]
            
            # Run in background thread to avoid blocking
            await asyncio.to_thread(
                subprocess.run, 
                cmd, 
                capture_output=True, 
                timeout=30, 
                text=True
            )
            
        except Exception as e:
            raise Exception(f"Windows SAPI error: {e}")
    
    async def _speak_system_fallback(self, text: str):
        """System fallback - just print the text"""
        print(f"💬 AI: {text}")
        # Simulate speech timing
        words = text.split()
        for word in words:
            if self.stop_speaking:
                break
            await asyncio.sleep(0.2)  # 200ms per word
    
    def print_performance_stats(self):
        """Print performance statistics"""
        print("\n📊 PERFORMANCE STATISTICS")
        print("=" * 40)
        print(f"STT Calls: {self.stats['stt_calls']}")
        print(f"Average STT RTF: {self.stats['avg_stt_rtf']:.3f} (Target: <0.35)")
        print(f"TTS Calls: {self.stats['tts_calls']}")
        print(f"Average TTS Latency: {self.stats['avg_tts_latency']:.3f}s")
        
        # Performance assessment
        stt_good = self.stats['avg_stt_rtf'] < 0.35 if self.stats['stt_calls'] > 0 else True
        tts_good = self.stats['avg_tts_latency'] < 2.0 if self.stats['tts_calls'] > 0 else True
        
        print(f"STT Performance: {'✅ GOOD' if stt_good else '❌ NEEDS IMPROVEMENT'}")
        print(f"TTS Performance: {'✅ GOOD' if tts_good else '❌ NEEDS IMPROVEMENT'}")
        print("=" * 40)

async def test_fixed_system():
    """Test the fixed voice system"""
    print("🧪 TESTING FIXED VOICE SYSTEM")
    print("=" * 40)
    
    voice_system = FixedVoiceSystem2025()
    
    # Test STT
    if voice_system.stt_available:
        print("\n🎤 Testing STT Performance...")
        test_audio = np.random.randn(16000 * 2).astype(np.int16)  # 2 seconds of noise
        result = await voice_system.transcribe_fixed(test_audio)
        print(f"STT Result: {result}")
    
    # Test TTS
    if voice_system.tts_available:
        print("\n🔊 Testing TTS Performance...")
        await voice_system.speak_fixed("Hello, this is a test of the fixed voice system.")
    
    # Print stats
    voice_system.print_performance_stats()
    
    print("\n🎉 FIXED VOICE SYSTEM TEST COMPLETE!")
    return voice_system

if __name__ == "__main__":
    asyncio.run(test_fixed_system())
