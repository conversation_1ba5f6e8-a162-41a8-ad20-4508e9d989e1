#!/usr/bin/env python3
"""
🎤 AUTO-INTERRUPT VOICE SYSTEM
AI automatically stops when you start speaking - completely hands-free
"""

import asyncio
import logging
import os
import tempfile
import wave
import threading
from datetime import datetime
from collections import deque

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("auto-interrupt")

class AutoInterruptVoice:
    """Voice system with automatic interruption when user speaks"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.sample_rate = 16000
        
        # State tracking
        self.is_ai_speaking = False
        self.is_user_speaking = False
        self.stop_speaking = False
        self.last_speech_time = 0
        self.silence_threshold = 1.5  # Seconds to wait after user stops
        
        # Audio monitoring
        self.audio_monitor_active = False
        self.speech_buffer = deque(maxlen=100)  # Store recent audio
        self.monitor_thread = None
        
        print("🎤 Auto-Interrupt Voice System")
        print("=" * 40)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize audio
        try:
            import sounddevice as sd
            print("✅ Audio: sounddevice available")
            print(f"   Input: {sd.default.device[0]}")
            print(f"   Output: {sd.default.device[1]}")
        except ImportError:
            print("❌ Audio: sounddevice not available")
            return False
        
        return True
    
    def detect_voice_activity(self, audio_chunk):
        """Detect if user is speaking"""
        try:
            # Convert to int16 for VAD
            if audio_chunk.dtype != np.int16:
                audio_chunk = (audio_chunk * 32767).astype(np.int16)
            
            # VAD requires 30ms frames (480 samples at 16kHz)
            frame_size = 480
            if len(audio_chunk) >= frame_size:
                frame = audio_chunk[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            return False
            
        except Exception:
            # Fallback to energy detection
            energy = np.sqrt(np.mean(audio_chunk.astype(np.float32) ** 2))
            return energy > 0.015  # Adjust threshold as needed
    
    def start_audio_monitoring(self):
        """Start continuous audio monitoring in background"""
        if self.audio_monitor_active:
            return
        
        self.audio_monitor_active = True
        
        def monitor_audio():
            try:
                import sounddevice as sd
                import time
                
                def audio_callback(indata, frames, time_info, status):
                    if not self.audio_monitor_active:
                        return
                    
                    # Convert to mono
                    if len(indata.shape) > 1:
                        audio_chunk = indata[:, 0]
                    else:
                        audio_chunk = indata.flatten()
                    
                    # Detect voice activity
                    has_speech = self.detect_voice_activity(audio_chunk)
                    current_time = time.time()
                    
                    if has_speech:
                        self.last_speech_time = current_time
                        
                        if not self.is_user_speaking:
                            self.is_user_speaking = True
                            print("\n🎤 User speaking...")
                            
                            # AUTOMATIC INTERRUPTION - Stop AI immediately
                            if self.is_ai_speaking:
                                print("⚡ Auto-interrupting AI!")
                                self.stop_speaking = True
                        
                        # Store audio for later transcription
                        self.speech_buffer.append(audio_chunk.copy())
                    
                    # Check if user stopped speaking
                    elif self.is_user_speaking:
                        silence_duration = current_time - self.last_speech_time
                        if silence_duration > self.silence_threshold:
                            self.is_user_speaking = False
                            print(f"🔇 User stopped ({silence_duration:.1f}s silence)")
                            
                            # Process what user said
                            if len(self.speech_buffer) > 0:
                                # Schedule transcription
                                asyncio.run_coroutine_threadsafe(
                                    self.process_user_speech(),
                                    asyncio.get_event_loop()
                                )
                
                # Start audio stream
                with sd.InputStream(
                    callback=audio_callback,
                    channels=1,
                    samplerate=self.sample_rate,
                    blocksize=1024,
                    dtype=np.float32
                ):
                    while self.audio_monitor_active:
                        time.sleep(0.1)
                        
            except Exception as e:
                print(f"❌ Audio monitoring error: {e}")
        
        # Start monitoring in background thread
        self.monitor_thread = threading.Thread(target=monitor_audio, daemon=True)
        self.monitor_thread.start()
        print("🎧 Background audio monitoring started")
    
    def stop_audio_monitoring(self):
        """Stop audio monitoring"""
        self.audio_monitor_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        print("🔇 Audio monitoring stopped")
    
    async def process_user_speech(self):
        """Process accumulated user speech"""
        if len(self.speech_buffer) == 0:
            return
        
        print("🔄 Processing speech...")
        
        # Combine audio chunks
        audio_data = np.concatenate(list(self.speech_buffer))
        self.speech_buffer.clear()
        
        # Transcribe
        transcription = await self.transcribe_audio(audio_data)
        
        if transcription.strip():
            print(f"📝 You: '{transcription}'")
            
            # Check for quit
            if 'quit' in transcription.lower() or 'exit' in transcription.lower():
                print("👋 Goodbye!")
                self.stop_audio_monitoring()
                return
            
            # Generate and speak response
            response = self.generate_response(transcription)
            await self.speak_with_auto_interrupt(response)
        else:
            print("❌ No clear speech detected")
    
    async def transcribe_audio(self, audio_data):
        """Transcribe audio data"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    # Convert to int16
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate response"""
        responses = {
            "hello": "Hello! I automatically stop when you start speaking - no buttons needed!",
            "hi": "Hi there! Just start talking anytime to interrupt me.",
            "test": "This is a test of the automatic interruption system. I'm speaking now, and if you start talking, I'll stop immediately without you pressing anything. The system continuously monitors your microphone.",
            "long": "This is a very long response to test automatic interruption. I'll keep talking for a while so you can test the hands-free interruption. Just start speaking at any time and I'll stop automatically. The system uses voice activity detection to know when you're speaking.",
            "how are you": "I'm doing great! The auto-interrupt system is working perfectly.",
            "interrupt": "Yes! I automatically detect when you start speaking and stop immediately. No buttons required!",
            "stop": "I stopped because I detected you speaking! The system works automatically.",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. I automatically waited until you finished speaking!"
    
    async def speak_with_auto_interrupt(self, text: str):
        """Speak text with automatic interruption"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            import edge_tts
            import sounddevice as sd
            import soundfile as sf
            
            # Generate TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Save audio
                await communicate.save(tmp_path.replace('.wav', '.mp3'))
                
                # Load audio
                data, fs = sf.read(tmp_path.replace('.wav', '.mp3'))
                
                # Play in very small chunks for instant interruption
                chunk_size = int(fs * 0.05)  # 50ms chunks for instant response
                
                for i in range(0, len(data), chunk_size):
                    # Check for interruption (user started speaking)
                    if self.stop_speaking:
                        print("🛑 Automatically interrupted!")
                        break
                    
                    chunk = data[i:i+chunk_size]
                    sd.play(chunk, fs)
                    sd.wait()
                    
                    # Very small delay to check interruption
                    await asyncio.sleep(0.01)
                
            finally:
                # Cleanup
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except (OSError, PermissionError):
                        pass
                        
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def run_conversation(self):
        """Run the auto-interrupt conversation"""
        print("\n🎙️ Auto-Interrupt Voice Conversation")
        print("=" * 50)
        print("✅ Completely hands-free operation")
        print("✅ AI stops automatically when you speak")
        print("✅ No buttons to press - just talk naturally")
        print("✅ Say 'quit' to exit")
        print("=" * 50)
        
        # Start background audio monitoring
        self.start_audio_monitoring()
        
        try:
            print("🎤 System ready! Start speaking...")
            print("   (The AI will respond and you can interrupt anytime)")
            
            # Keep the conversation running
            while self.audio_monitor_active:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n👋 Conversation ended!")
        finally:
            self.stop_audio_monitoring()
    
    async def run(self):
        """Main run function"""
        if not self.stt_model or not self.vad:
            print("❌ System not properly initialized")
            return
        
        await self.run_conversation()

async def main():
    """Main function"""
    voice = AutoInterruptVoice()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
