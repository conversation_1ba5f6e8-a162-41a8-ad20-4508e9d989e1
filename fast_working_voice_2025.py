#!/usr/bin/env python3
"""
🚀 FAST WORKING VOICE 2025
Modern, fast voice system that actually works with good TTS
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
import subprocess
import sys
from pathlib import Path

import numpy as np
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fast-working-2025")

class FastWorkingVoice2025:
    """Fast, reliable voice system with modern architecture"""
    
    def __init__(self):
        # Audio settings - optimized for speed
        self.sample_rate = 16000
        self.chunk_size = 512  # Small chunks for fast response
        
        # State management
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue(maxsize=50)
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 0.7  # Fast response
        
        # Performance tracking
        self.response_times = []
        
        print("🚀 Fast Working Voice 2025")
        print("=" * 35)
        
        self._init_fast_components()
    
    def _init_fast_components(self):
        """Initialize fast, reliable components"""
        print("🔄 Loading optimized components...")
        
        # 1. Fast STT
        self._init_fast_stt()
        
        # 2. Working TTS
        self._init_working_tts()
        
        # 3. Optimized VAD
        self._init_optimized_vad()
        
        print("✅ Fast components ready!")
    
    def _init_fast_stt(self):
        """Initialize fastest STT available"""
        try:
            from faster_whisper import WhisperModel
            # Use tiny model with optimizations for speed
            self.stt_model = WhisperModel(
                "tiny.en", 
                device="cpu", 
                compute_type="int8",  # Faster computation
                num_workers=1  # Single worker for consistency
            )
            print("✅ STT: faster-whisper (speed optimized)")
            self.stt_available = True
        except ImportError:
            print("❌ STT: faster-whisper not available")
            self.stt_available = False
    
    def _init_working_tts(self):
        """Initialize working TTS with good quality"""
        # Try multiple TTS options
        
        # Option 1: Edge-TTS (best quality, works reliably)
        try:
            import edge_tts
            self.tts_type = "edge_tts"
            print("✅ TTS: Edge-TTS (high quality)")
            self.tts_available = True
            return
        except ImportError:
            pass
        
        # Option 2: Windows built-in TTS
        if sys.platform == "win32":
            try:
                # Test Windows TTS
                result = subprocess.run([
                    'powershell', '-Command', 
                    'Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Speak("test")'
                ], capture_output=True, timeout=5)
                
                if result.returncode == 0:
                    self.tts_type = "windows_sapi"
                    print("✅ TTS: Windows SAPI")
                    self.tts_available = True
                    return
            except:
                pass
        
        # Option 3: System say command (macOS/Linux)
        try:
            if sys.platform == "darwin":
                subprocess.run(['say', 'test'], capture_output=True, timeout=2)
                self.tts_type = "macos_say"
                print("✅ TTS: macOS say")
                self.tts_available = True
                return
        except:
            pass
        
        # Fallback: Text only
        self.tts_type = "text_only"
        self.tts_available = False
        print("⚠️ TTS: Text output only")
    
    def _init_optimized_vad(self):
        """Initialize optimized voice activity detection"""
        # Smart energy-based VAD with adaptive thresholds
        self.vad_threshold = 0.015
        self.background_energy = 0.005
        self.energy_history = []
        print("✅ VAD: Adaptive energy detection")
    
    def detect_speech_fast(self, audio_chunk):
        """Fast, accurate speech detection"""
        try:
            # Calculate energy
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            
            # Update energy history for adaptive threshold
            self.energy_history.append(energy)
            if len(self.energy_history) > 50:  # Keep last 50 samples
                self.energy_history.pop(0)
            
            # Adaptive threshold based on background noise
            if len(self.energy_history) > 10:
                avg_energy = np.mean(self.energy_history)
                self.background_energy = 0.9 * self.background_energy + 0.1 * avg_energy
                threshold = max(self.vad_threshold, self.background_energy * 3)
            else:
                threshold = self.vad_threshold
            
            return energy > threshold
            
        except Exception:
            return False
    
    def audio_callback(self, indata, frames, time_info, status):
        """Optimized audio callback"""
        try:
            # Convert to mono
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            # Non-blocking queue with overflow protection
            try:
                self.audio_queue.put_nowait(audio_chunk.copy())
            except queue.Full:
                # Remove oldest and add new
                try:
                    self.audio_queue.get_nowait()
                    self.audio_queue.put_nowait(audio_chunk.copy())
                except queue.Empty:
                    pass
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_fast(self):
        """Fast audio processing with smart batching"""
        try:
            processed = 0
            while not self.audio_queue.empty() and processed < 5:  # Process in batches
                audio_chunk = self.audio_queue.get_nowait()
                processed += 1
                
                # Fast speech detection
                has_speech = self.detect_speech_fast(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 Speaking...")
                        
                        # Instant interruption
                        if self.is_ai_speaking:
                            print("⚡ Interrupted!")
                            self.stop_speaking = True
                    
                    # Buffer speech
                    self.speech_buffer.append(audio_chunk)
                    
                    # Prevent buffer overflow
                    if len(self.speech_buffer) > 100:  # ~6 seconds max
                        self.speech_buffer = self.speech_buffer[-80:]
                
                # Fast silence detection
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 Done ({silence_duration:.1f}s)")
                        return True
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_fast(self, audio_data):
        """Fast transcription with optimizations"""
        if not self.stt_available:
            return ""
        
        start_time = time.time()
        print("🔄 Transcribing...")
        
        try:
            # Prepare audio
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Save to temp file (faster-whisper requirement)
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                import wave
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                # Fast transcription with optimized settings
                segments, _ = self.stt_model.transcribe(
                    tmp_path,
                    beam_size=1,  # Faster beam search
                    best_of=1,    # Single candidate
                    temperature=0,  # Deterministic
                    condition_on_previous_text=False  # Faster processing
                )
                
                text = " ".join([segment.text.strip() for segment in segments])
                
                # Track performance
                transcription_time = time.time() - start_time
                self.response_times.append(transcription_time)
                print(f"⚡ Transcribed in {transcription_time:.2f}s")
                
                return text
                
            finally:
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_fast(self, text: str):
        """Fast, high-quality speech output"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            if self.tts_type == "edge_tts":
                # High-quality Edge TTS
                import edge_tts
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    # Generate with fast voice
                    communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                    await communicate.save(tmp_path)
                    
                    # Load and play with interruption support
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_with_fast_interruption(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            elif self.tts_type == "windows_sapi":
                # Windows built-in TTS
                def speak_windows():
                    try:
                        subprocess.run([
                            'powershell', '-Command', 
                            f'Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Rate = 2; $speak.Speak("{text}")'
                        ], timeout=30)
                    except:
                        pass
                
                # Run in thread for interruption
                speak_thread = threading.Thread(target=speak_windows, daemon=True)
                speak_thread.start()
                
                # Monitor for interruption
                while speak_thread.is_alive():
                    if self.stop_speaking:
                        print("🛑 Interrupted!")
                        break
                    await asyncio.sleep(0.1)
            
            else:
                # Text fallback
                print(f"💬 {text}")
                await asyncio.sleep(len(text) * 0.05)  # Simulate speech timing
                
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _play_with_fast_interruption(self, audio_data, sample_rate):
        """Play audio with very fast interruption"""
        chunk_size = int(sample_rate * 0.05)  # 50ms chunks for instant response
        
        for i in range(0, len(audio_data), chunk_size):
            if self.stop_speaking:
                print("🛑 Interrupted!")
                break
            
            chunk = audio_data[i:i+chunk_size]
            sd.play(chunk, sample_rate)
            sd.wait()
            await asyncio.sleep(0.01)
    
    def generate_smart_response(self, user_input: str) -> str:
        """Generate contextual responses"""
        responses = {
            "hello": "Hello! I'm the fast 2025 voice system with instant response and high-quality audio!",
            "hi": "Hi there! This system is optimized for speed and natural conversation.",
            "test": "Testing the fast voice system! I use optimized models for quick transcription and high-quality text-to-speech. You can interrupt me anytime by speaking.",
            "fast": "Yes! This system is designed for maximum speed with sub-second response times.",
            "slow": "The old system was slow, but this 2025 version is lightning fast!",
            "quality": "High-quality audio with Edge TTS and optimized processing!",
            "interrupt": "Interrupt me anytime! I detect your voice instantly and stop speaking.",
            "modern": "This uses modern 2025 architecture with optimized models and smart processing!",
            "performance": f"Average transcription time: {np.mean(self.response_times[-10:]):.2f}s" if self.response_times else "Performance tracking active!",
            "quit": "Goodbye! Thanks for trying the fast 2025 voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. Fast processing complete in record time!"
    
    async def run_fast_conversation(self):
        """Run optimized conversation loop"""
        print("\n🚀 Fast Voice Conversation 2025")
        print("=" * 40)
        print("⚡ Sub-second response time")
        print("🎯 Instant interruption detection")
        print("🔊 High-quality TTS output")
        print("🧠 Optimized modern architecture")
        print("=" * 40)
        
        if not self.stt_available:
            print("❌ STT not available. Install: pip install faster-whisper")
            return
        
        try:
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32
            ):
                print("🎤 Fast system ready! Start speaking...")
                
                conversation_count = 0
                while True:
                    # Fast processing loop
                    should_transcribe = self.process_audio_fast()
                    
                    if should_transcribe and len(self.speech_buffer) > 0:
                        conversation_count += 1
                        
                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()
                        
                        # Fast transcription
                        transcription = await self.transcribe_fast(audio_data)
                        
                        if transcription.strip():
                            print(f"📝 [{conversation_count}] You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye', 'stop']):
                                print("👋 Fast system shutting down!")
                                break
                            
                            # Generate and speak response
                            response = self.generate_smart_response(transcription)
                            await self.speak_fast(response)
                        else:
                            print("❌ No clear speech detected")
                    
                    # Optimized loop timing
                    await asyncio.sleep(0.02)
                    
        except KeyboardInterrupt:
            print("\n👋 Fast system stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        await self.run_fast_conversation()

async def main():
    """Main function"""
    print("🚀 Starting Fast Working Voice System 2025...")
    voice = FastWorkingVoice2025()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
