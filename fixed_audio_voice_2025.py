#!/usr/bin/env python3
"""
🔊 FIXED AUDIO VOICE 2025
Specifically designed to work with your Arctis 7 headphones
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
import subprocess
import sys
from pathlib import Path

import numpy as np
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fixed-audio-2025")

class FixedAudioVoice2025:
    """Voice system with guaranteed audio output"""
    
    def __init__(self):
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        
        # State management
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue()
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 1.0
        
        # Audio device info
        self.output_device = None
        self.input_device = None
        
        print("🔊 Fixed Audio Voice 2025")
        print("=" * 30)
        
        self._setup_audio_devices()
        self._init_components()
    
    def _setup_audio_devices(self):
        """Setup and test audio devices specifically for Arctis 7"""
        print("🎧 Setting up audio devices...")
        
        # List all audio devices
        devices = sd.query_devices()
        print("\n📋 Available Audio Devices:")
        for i, device in enumerate(devices):
            device_type = []
            if device['max_input_channels'] > 0:
                device_type.append("INPUT")
            if device['max_output_channels'] > 0:
                device_type.append("OUTPUT")
            
            print(f"  {i}: {device['name']} - {'/'.join(device_type)}")
            
            # Look for Arctis 7 devices
            if 'arctis' in device['name'].lower():
                if 'game' in device['name'].lower() and device['max_output_channels'] > 0:
                    self.output_device = i
                    print(f"  🎯 Found Arctis 7 Game (Output): Device {i}")
                elif 'chat' in device['name'].lower() and device['max_input_channels'] > 0:
                    self.input_device = i
                    print(f"  🎯 Found Arctis 7 Chat (Input): Device {i}")
        
        # Set default devices if Arctis not found
        if self.output_device is None:
            self.output_device = sd.default.device[1]  # Default output
            print(f"🔊 Using default output device: {self.output_device}")
        
        if self.input_device is None:
            self.input_device = sd.default.device[0]  # Default input
            print(f"🎤 Using default input device: {self.input_device}")
        
        # Test audio output with multiple methods
        self._test_audio_output_methods()
    
    def _test_audio_output_methods(self):
        """Test multiple audio output methods"""
        print("\n🔊 Testing audio output methods...")
        
        # Method 1: Direct sounddevice test
        try:
            print("🧪 Testing Method 1: Direct sounddevice...")
            duration = 0.5
            frequency = 440
            t = np.linspace(0, duration, int(self.sample_rate * duration), False)
            test_tone = 0.5 * np.sin(2 * np.pi * frequency * t)
            
            sd.play(test_tone, self.sample_rate, device=self.output_device)
            sd.wait()
            print("✅ Method 1: Direct sounddevice - SUCCESS")
            self.audio_method = "sounddevice"
            
        except Exception as e:
            print(f"❌ Method 1 failed: {e}")
            
            # Method 2: Windows system audio
            if sys.platform == "win32":
                try:
                    print("🧪 Testing Method 2: Windows system audio...")
                    result = subprocess.run([
                        'powershell', '-Command', 
                        '''
                        [console]::beep(440, 500)
                        '''
                    ], capture_output=True, timeout=3)
                    
                    if result.returncode == 0:
                        print("✅ Method 2: Windows system audio - SUCCESS")
                        self.audio_method = "windows_system"
                    else:
                        raise Exception("Windows beep failed")
                        
                except Exception as e:
                    print(f"❌ Method 2 failed: {e}")
                    
                    # Method 3: Windows Media Player
                    try:
                        print("🧪 Testing Method 3: Windows Media Player...")
                        self.audio_method = "windows_media"
                        print("✅ Method 3: Windows Media Player - READY")
                        
                    except Exception as e:
                        print(f"❌ Method 3 failed: {e}")
                        self.audio_method = "text_only"
            else:
                self.audio_method = "text_only"
        
        print(f"🎯 Selected audio method: {self.audio_method}")
    
    def _init_components(self):
        """Initialize voice components"""
        print("🔄 Loading voice components...")
        
        # STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("base.en", device="cpu")
            print("✅ STT: faster-whisper")
            self.stt_available = True
        except ImportError:
            print("❌ STT: Not available")
            self.stt_available = False
        
        # TTS
        try:
            import edge_tts
            print("✅ TTS: Edge-TTS")
            self.tts_available = True
        except ImportError:
            print("❌ TTS: Not available")
            self.tts_available = False
        
        print("✅ Components ready!")
    
    def detect_speech(self, audio_chunk):
        """Simple speech detection"""
        energy = np.sqrt(np.mean(audio_chunk ** 2))
        return energy > 0.02
    
    def audio_callback(self, indata, frames, time_info, status):
        """Audio input callback"""
        try:
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            self.audio_queue.put(audio_chunk.copy())
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio(self):
        """Process audio for speech detection"""
        try:
            while not self.audio_queue.empty():
                audio_chunk = self.audio_queue.get_nowait()
                
                has_speech = self.detect_speech(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 Speaking...")
                        
                        if self.is_ai_speaking:
                            print("⚡ Interrupted!")
                            self.stop_speaking = True
                    
                    self.speech_buffer.append(audio_chunk)
                
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 Done ({silence_duration:.1f}s)")
                        return True
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe(self, audio_data):
        """Transcribe audio"""
        if not self.stt_available:
            return ""
        
        try:
            print("🔄 Transcribing...")
            
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                import wave
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_with_guaranteed_audio(self, text: str):
        """Speak with guaranteed audio output"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            if self.audio_method == "sounddevice":
                await self._speak_sounddevice(text)
            elif self.audio_method == "windows_system":
                await self._speak_windows_system(text)
            elif self.audio_method == "windows_media":
                await self._speak_windows_media(text)
            else:
                print(f"💬 AI: {text}")
                await asyncio.sleep(len(text) * 0.08)
                
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (Audio error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _speak_sounddevice(self, text: str):
        """Speak using sounddevice with maximum volume"""
        import edge_tts
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            # Generate TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            await communicate.save(tmp_path.replace('.wav', '.mp3'))
            
            # Load and process audio
            audio_data, sample_rate = sf.read(tmp_path.replace('.wav', '.mp3'))
            
            # Convert to mono if stereo
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Amplify audio for better hearing
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data)) * 0.95  # Maximum safe volume
            
            # Play with specific device and maximum volume
            chunk_size = int(sample_rate * 0.1)  # 100ms chunks
            
            for i in range(0, len(audio_data), chunk_size):
                if self.stop_speaking:
                    print("🛑 Audio interrupted!")
                    break
                
                chunk = audio_data[i:i+chunk_size]
                
                if len(chunk) > 0:
                    # Play at maximum volume to specific device
                    sd.play(chunk, sample_rate, device=self.output_device)
                    sd.wait()
                
                await asyncio.sleep(0.01)
            
        finally:
            for ext in ['.wav', '.mp3']:
                try:
                    path = tmp_path.replace('.wav', ext)
                    if os.path.exists(path):
                        os.unlink(path)
                except:
                    pass
    
    async def _speak_windows_system(self, text: str):
        """Speak using Windows system TTS"""
        def speak_windows():
            try:
                subprocess.run([
                    'powershell', '-Command', 
                    f'''
                    Add-Type -AssemblyName System.Speech
                    $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer
                    $speak.Volume = 100
                    $speak.Rate = 0
                    $speak.Speak("{text}")
                    '''
                ], timeout=60)
            except Exception as e:
                print(f"Windows TTS error: {e}")
        
        speak_thread = threading.Thread(target=speak_windows, daemon=True)
        speak_thread.start()
        
        while speak_thread.is_alive():
            if self.stop_speaking:
                print("🛑 Interrupted!")
                break
            await asyncio.sleep(0.1)
    
    async def _speak_windows_media(self, text: str):
        """Speak using Windows Media Player"""
        import edge_tts
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            # Generate TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            await communicate.save(tmp_path.replace('.wav', '.mp3'))
            
            # Play with Windows Media Player
            def play_with_media_player():
                try:
                    subprocess.run([
                        'powershell', '-Command', 
                        f'''
                        Add-Type -AssemblyName presentationCore
                        $mediaPlayer = New-Object system.windows.media.mediaplayer
                        $mediaPlayer.open("{tmp_path.replace('.wav', '.mp3')}")
                        $mediaPlayer.Volume = 1.0
                        $mediaPlayer.Play()
                        Start-Sleep -Seconds {len(text) * 0.1}
                        '''
                    ], timeout=60)
                except Exception as e:
                    print(f"Media player error: {e}")
            
            play_thread = threading.Thread(target=play_with_media_player, daemon=True)
            play_thread.start()
            
            while play_thread.is_alive():
                if self.stop_speaking:
                    print("🛑 Interrupted!")
                    break
                await asyncio.sleep(0.1)
            
        finally:
            for ext in ['.wav', '.mp3']:
                try:
                    path = tmp_path.replace('.wav', ext)
                    if os.path.exists(path):
                        os.unlink(path)
                except:
                    pass
    
    def generate_response(self, user_input: str) -> str:
        """Generate responses"""
        responses = {
            "hello": "Hello! I'm testing multiple audio methods to make sure you can hear me!",
            "test": "This is an audio test! I'm using your Arctis 7 headphones and trying different methods to ensure you can hear my voice clearly.",
            "audio": "Testing audio output! Can you hear me now? I'm using maximum volume and targeting your specific audio device.",
            "hear": "Can you hear me? I'm speaking through your Arctis 7 Game headphones at maximum volume!",
            "loud": "I'm speaking as loud as possible! The audio should be coming through your headphones clearly.",
            "volume": "Volume is set to maximum! You should definitely be able to hear me now.",
            "quit": "Goodbye! I hope you were able to hear me speaking!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. Can you hear my response through your headphones?"
    
    async def run_conversation(self):
        """Run conversation with guaranteed audio"""
        print("\n🔊 Fixed Audio Voice Conversation")
        print("=" * 35)
        print(f"🎧 Output device: {self.output_device}")
        print(f"🎤 Input device: {self.input_device}")
        print(f"🔊 Audio method: {self.audio_method}")
        print("=" * 35)
        
        if not self.stt_available:
            print("❌ STT not available")
            return
        
        try:
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32,
                device=self.input_device
            ):
                print("🎤 System ready! Start speaking...")
                print("   Say 'test' to hear a loud audio test")
                print("   Say 'audio' to test audio output")
                
                while True:
                    should_transcribe = self.process_audio()
                    
                    if should_transcribe and len(self.speech_buffer) > 0:
                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()
                        
                        # Transcribe
                        transcription = await self.transcribe(audio_data)
                        
                        if transcription.strip():
                            print(f"📝 You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye']):
                                print("👋 System shutting down!")
                                break
                            
                            # Generate and speak response
                            response = self.generate_response(transcription)
                            await self.speak_with_guaranteed_audio(response)
                        else:
                            print("❌ No speech detected")
                    
                    await asyncio.sleep(0.05)
                    
        except KeyboardInterrupt:
            print("\n👋 System stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        await self.run_conversation()

async def main():
    """Main function"""
    print("🔊 Starting Fixed Audio Voice System 2025...")
    voice = FixedAudioVoice2025()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
