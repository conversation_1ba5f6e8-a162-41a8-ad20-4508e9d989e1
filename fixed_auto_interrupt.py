#!/usr/bin/env python3
"""
🎤 FIXED AUTO-INTERRUPT VOICE SYSTEM
Simple, reliable voice system where AI stops when you speak
"""

import asyncio
import logging
import os
import tempfile
import wave
import time
import queue
import threading

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fixed-auto-interrupt")

class FixedAutoInterrupt:
    """Simple, reliable auto-interrupt voice system"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.sample_rate = 16000
        
        # State tracking
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speech_detected = False
        
        # Audio processing
        self.audio_queue = queue.Queue()
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 1.5
        
        print("🎤 Fixed Auto-Interrupt Voice System")
        print("=" * 45)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize audio
        try:
            import sounddevice as sd
            print("✅ Audio: sounddevice available")
        except ImportError:
            print("❌ Audio: sounddevice not available")
            return False
        
        return True
    
    def detect_voice_activity(self, audio_chunk):
        """Detect if user is speaking"""
        try:
            # Convert to int16 for VAD
            if audio_chunk.dtype != np.int16:
                audio_chunk = (audio_chunk * 32767).astype(np.int16)
            
            # VAD requires 30ms frames
            frame_size = 480  # 30ms at 16kHz
            if len(audio_chunk) >= frame_size:
                frame = audio_chunk[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            return False
            
        except Exception:
            # Fallback to energy detection
            energy = np.sqrt(np.mean(audio_chunk.astype(np.float32) ** 2))
            return energy > 0.02
    
    def audio_callback(self, indata, frames, time_info, status):
        """Audio callback - runs in separate thread"""
        try:
            # Convert to mono
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            # Put audio in queue for main thread processing
            self.audio_queue.put(audio_chunk.copy())
            
        except Exception as e:
            print(f"Audio callback error: {e}")
    
    def process_audio_queue(self):
        """Process audio from queue (runs in main thread)"""
        try:
            # Process all available audio chunks
            while not self.audio_queue.empty():
                audio_chunk = self.audio_queue.get_nowait()
                
                # Detect voice activity
                has_speech = self.detect_voice_activity(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speech_detected:
                        self.user_speech_detected = True
                        print("\n🎤 User speaking...")
                        
                        # AUTO-INTERRUPT: Stop AI immediately
                        if self.is_ai_speaking:
                            print("⚡ Auto-interrupting AI!")
                            self.stop_speaking = True
                    
                    # Store audio for transcription
                    self.speech_buffer.append(audio_chunk)
                
                # Check if user stopped speaking
                elif self.user_speech_detected:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speech_detected = False
                        print(f"🔇 User stopped ({silence_duration:.1f}s silence)")
                        
                        # Process the speech
                        if len(self.speech_buffer) > 0:
                            return True  # Signal to process speech
                            
        except queue.Empty:
            pass
        except Exception as e:
            print(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_speech_buffer(self):
        """Transcribe accumulated speech"""
        if len(self.speech_buffer) == 0:
            return ""
        
        print("🔄 Transcribing...")
        
        # Combine audio chunks
        audio_data = np.concatenate(self.speech_buffer)
        self.speech_buffer.clear()
        
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    # Convert to int16
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate response"""
        responses = {
            "hello": "Hello! I automatically stop speaking when you start talking!",
            "hi": "Hi! The auto-interrupt system is working - just start speaking anytime.",
            "test": "This is a test of automatic interruption. I'm speaking now, and if you start talking, I'll stop immediately without any button presses. The system monitors your voice continuously.",
            "long": "This is a longer response to test the auto-interrupt feature. I'll keep talking so you can try interrupting me by simply speaking. The moment you start talking, I should stop automatically. This demonstrates hands-free conversation control.",
            "how are you": "I'm doing great! The voice system is working smoothly with automatic interruption.",
            "interrupt": "Yes! I detect your voice automatically and stop speaking immediately. No buttons needed!",
            "quit": "Goodbye! The auto-interrupt system worked perfectly!",
            "exit": "Goodbye! Thanks for testing the voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. I automatically waited until you finished speaking!"
    
    async def speak_with_interrupt(self, text: str):
        """Speak text with automatic interruption"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            import edge_tts
            import sounddevice as sd
            import soundfile as sf
            
            # Generate TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Save audio
                await communicate.save(tmp_path.replace('.wav', '.mp3'))
                
                # Load audio
                data, fs = sf.read(tmp_path.replace('.wav', '.mp3'))
                
                # Play in small chunks for quick interruption
                chunk_size = int(fs * 0.1)  # 100ms chunks
                
                for i in range(0, len(data), chunk_size):
                    # Check for interruption
                    if self.stop_speaking:
                        print("🛑 Automatically interrupted!")
                        break
                    
                    chunk = data[i:i+chunk_size]
                    sd.play(chunk, fs)
                    sd.wait()
                    
                    # Small delay to check for interruption
                    await asyncio.sleep(0.02)
                
            finally:
                # Cleanup
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except (OSError, PermissionError):
                        pass
                        
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def run_conversation(self):
        """Run the conversation with auto-interrupt"""
        print("\n🎙️ Auto-Interrupt Conversation")
        print("=" * 40)
        print("✅ Speak naturally - AI stops when you talk")
        print("✅ No buttons needed - completely hands-free")
        print("✅ Say 'quit' or 'exit' to stop")
        print("=" * 40)
        
        try:
            import sounddevice as sd
            
            # Start audio stream
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=1024,
                dtype=np.float32
            ):
                print("🎤 Listening... Start speaking!")
                
                conversation_active = True
                while conversation_active:
                    # Process audio queue
                    should_transcribe = self.process_audio_queue()
                    
                    if should_transcribe:
                        # Transcribe user speech
                        transcription = await self.transcribe_speech_buffer()
                        
                        if transcription.strip():
                            print(f"📝 You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye']):
                                print("👋 Goodbye!")
                                conversation_active = False
                                break
                            
                            # Generate and speak response
                            response = self.generate_response(transcription)
                            await self.speak_with_interrupt(response)
                        else:
                            print("❌ No clear speech detected")
                    
                    # Small delay to prevent busy waiting
                    await asyncio.sleep(0.1)
                    
        except KeyboardInterrupt:
            print("\n👋 Conversation ended!")
        except Exception as e:
            print(f"❌ Conversation error: {e}")
    
    async def run(self):
        """Main run function"""
        if not self.stt_model or not self.vad:
            print("❌ System not properly initialized")
            return
        
        await self.run_conversation()

async def main():
    """Main function"""
    voice = FixedAutoInterrupt()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
