#!/usr/bin/env python3
"""
🎤 VOICE SYSTEM LAUNCHER
Simple launcher for the optimized voice system
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# Apply all voice optimizations
try:
    import project  # This applies all the voice patches and optimizations
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("voice-launcher")

class VoiceSystemLauncher:
    """Simple voice system launcher"""
    
    def __init__(self):
        self.dependencies_ok = False
        self.system_ready = False
        
    def check_dependencies(self):
        """Check if all required dependencies are available"""
        print("\n🔍 Checking Dependencies...")
        print("-" * 40)
        
        deps_status = {}
        
        # Check faster-whisper
        try:
            import faster_whisper
            deps_status['faster_whisper'] = True
            print("✅ faster-whisper: Available")
        except ImportError:
            deps_status['faster_whisper'] = False
            print("❌ faster-whisper: Missing")
        
        # Check webrtcvad
        try:
            import webrtcvad
            deps_status['webrtcvad'] = True
            print("✅ webrtcvad: Available")
        except ImportError:
            deps_status['webrtcvad'] = False
            print("❌ webrtcvad: Missing")
        
        # Check numpy
        try:
            import numpy
            deps_status['numpy'] = True
            print("✅ numpy: Available")
        except ImportError:
            deps_status['numpy'] = False
            print("❌ numpy: Missing")
        
        # Check ollama (optional)
        try:
            import ollama
            deps_status['ollama'] = True
            print("✅ ollama: Available")
        except ImportError:
            deps_status['ollama'] = False
            print("⚠️ ollama: Missing (optional)")
        
        # Check edge-tts (optional)
        try:
            import edge_tts
            deps_status['edge_tts'] = True
            print("✅ edge-tts: Available")
        except ImportError:
            deps_status['edge_tts'] = False
            print("⚠️ edge-tts: Missing (optional)")
        
        # Check if core dependencies are met
        core_deps = ['faster_whisper', 'webrtcvad', 'numpy']
        self.dependencies_ok = all(deps_status.get(dep, False) for dep in core_deps)
        
        print(f"\n📊 Dependencies Status: {'✅ Ready' if self.dependencies_ok else '❌ Missing core dependencies'}")
        
        if not self.dependencies_ok:
            print("\n🔧 To install missing dependencies, run:")
            print("   pip install -r requirements.txt")
        
        return self.dependencies_ok
    
    def run_system_test(self):
        """Run the voice system test"""
        if not self.dependencies_ok:
            print("❌ Cannot run tests - dependencies missing")
            return False
        
        print("\n🧪 Running System Tests...")
        print("-" * 40)
        
        try:
            # Import and run the test
            from run_local_voice_test import LocalVoiceTest
            
            async def run_test():
                test = LocalVoiceTest()
                await test.run_full_test()
                return True
            
            # Run the test
            result = asyncio.run(run_test())
            self.system_ready = True
            print("\n✅ All tests passed! System is ready.")
            return True
            
        except Exception as e:
            logger.error(f"System test failed: {e}")
            print(f"\n❌ System test failed: {e}")
            return False
    
    def launch_simple_chat(self):
        """Launch simple voice chat"""
        if not self.system_ready:
            print("❌ System not ready - run tests first")
            return
        
        print("\n🎤 Launching Simple Voice Chat...")
        print("-" * 40)
        
        try:
            # Try to launch the simple voice chat
            import subprocess
            result = subprocess.run([
                sys.executable, "simple_voice_chat.py"
            ], cwd=Path(__file__).parent)
            
        except Exception as e:
            logger.error(f"Failed to launch voice chat: {e}")
            print(f"❌ Failed to launch voice chat: {e}")
    
    def launch_ollama_integration(self):
        """Launch Ollama voice integration"""
        if not self.system_ready:
            print("❌ System not ready - run tests first")
            return
        
        print("\n🤖 Launching Ollama Voice Integration...")
        print("-" * 40)
        
        try:
            # Check if ollama is available
            import ollama
            
            # Try to launch the ollama integration
            import subprocess
            result = subprocess.run([
                sys.executable, "simple_ollama_chat.py"
            ], cwd=Path(__file__).parent)
            
        except ImportError:
            print("❌ Ollama not available. Install with: pip install ollama")
        except Exception as e:
            logger.error(f"Failed to launch Ollama integration: {e}")
            print(f"❌ Failed to launch Ollama integration: {e}")
    
    def show_menu(self):
        """Show the main menu"""
        print("\n🎤 VOICE SYSTEM LAUNCHER")
        print("=" * 40)
        print("1. Check Dependencies")
        print("2. Run System Tests")
        print("3. Launch Simple Voice Chat")
        print("4. Launch Ollama Voice Integration")
        print("5. Exit")
        print("-" * 40)
        
        choice = input("Select option (1-5): ").strip()
        return choice
    
    def run(self):
        """Main launcher loop"""
        print("🎤 Welcome to the Voice System Launcher!")
        
        while True:
            choice = self.show_menu()
            
            if choice == "1":
                self.check_dependencies()
            elif choice == "2":
                self.run_system_test()
            elif choice == "3":
                self.launch_simple_chat()
            elif choice == "4":
                self.launch_ollama_integration()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-5.")
            
            input("\nPress Enter to continue...")

def main():
    """Main function"""
    launcher = VoiceSystemLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
