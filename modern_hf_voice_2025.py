#!/usr/bin/env python3
"""
🚀 MODERN HUGGING FACE VOICE SYSTEM 2025
Latest HF models with working TTS and modern interruption
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
from pathlib import Path

import numpy as np
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("modern-hf-2025")

class ModernHFVoice2025:
    """Modern voice system with latest Hugging Face models"""
    
    def __init__(self):
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        
        # State management
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue()
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 1.0
        
        # Models
        self.device = "cpu"  # Will auto-detect GPU if available
        
        print("🚀 Modern Hugging Face Voice System 2025")
        print("=" * 50)
        
        self._install_and_init_models()
    
    def _install_and_init_models(self):
        """Install and initialize latest HF models"""
        print("📦 Installing/updating latest packages...")
        
        # Install required packages
        packages = [
            "torch",
            "transformers>=4.35.0",
            "datasets",
            "accelerate", 
            "TTS>=0.22.0",
            "speechbrain",
            "librosa",
            "scipy"
        ]
        
        for package in packages:
            try:
                __import__(package.split('>=')[0].split('==')[0])
                print(f"✅ {package} already available")
            except ImportError:
                print(f"📥 Installing {package}...")
                os.system(f"pip install {package}")
        
        self._init_modern_models()
    
    def _init_modern_models(self):
        """Initialize latest 2025 HF models"""
        print("🔄 Loading latest 2025 models...")
        
        # 1. Modern STT with Whisper v3 Turbo
        self._init_modern_stt()
        
        # 2. Latest HF TTS models
        self._init_modern_hf_tts()
        
        # 3. Modern VAD
        self._init_modern_vad()
        
        print("🚀 All modern models loaded!")
    
    def _init_modern_stt(self):
        """Initialize latest STT models"""
        try:
            print("📥 Loading Whisper v3 Turbo...")
            from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline
            import torch
            
            # Check for GPU
            if torch.cuda.is_available():
                self.device = "cuda"
                torch_dtype = torch.float16
                print(f"🚀 Using GPU: {torch.cuda.get_device_name()}")
            else:
                torch_dtype = torch.float32
                print("💻 Using CPU")
            
            # Load latest Whisper model
            model_id = "openai/whisper-large-v3-turbo"
            
            model = AutoModelForSpeechSeq2Seq.from_pretrained(
                model_id,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True,
                use_safetensors=True,
            )
            model.to(self.device)
            
            processor = AutoProcessor.from_pretrained(model_id)
            
            self.stt_pipeline = pipeline(
                "automatic-speech-recognition",
                model=model,
                tokenizer=processor.tokenizer,
                feature_extractor=processor.feature_extractor,
                max_new_tokens=128,
                chunk_length_s=30,
                batch_size=16,
                return_timestamps=True,
                torch_dtype=torch_dtype,
                device=self.device,
            )
            
            print("✅ STT: Whisper v3 Turbo loaded")
            self.stt_available = True
            
        except Exception as e:
            print(f"⚠️ Modern STT failed: {e}")
            # Fallback to faster-whisper
            try:
                from faster_whisper import WhisperModel
                self.stt_model = WhisperModel("base.en", device="cpu")
                self.stt_type = "faster_whisper"
                print("✅ STT: faster-whisper fallback")
                self.stt_available = True
            except:
                print("❌ No STT available")
                self.stt_available = False
    
    def _init_modern_hf_tts(self):
        """Initialize latest Hugging Face TTS models"""
        print("📥 Loading modern HF TTS models...")
        
        # Option 1: Try Coqui TTS with latest models
        try:
            from TTS.api import TTS
            
            # List available models
            print("🔍 Available TTS models:")
            available_models = TTS.list_models()
            
            # Try XTTS v2 (best quality)
            try:
                self.tts_model = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
                self.tts_type = "xtts_v2"
                print("✅ TTS: XTTS v2 (state-of-the-art)")
                self.tts_available = True
                return
            except Exception as e:
                print(f"⚠️ XTTS v2 failed: {e}")
            
            # Try Tacotron2 + WaveGlow
            try:
                self.tts_model = TTS("tts_models/en/ljspeech/tacotron2-DDC_ph")
                self.tts_type = "tacotron2"
                print("✅ TTS: Tacotron2 (high quality)")
                self.tts_available = True
                return
            except Exception as e:
                print(f"⚠️ Tacotron2 failed: {e}")
                
        except ImportError:
            print("📦 Installing TTS...")
            os.system("pip install TTS")
        
        # Option 2: Try SpeechT5 from Transformers
        try:
            from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan
            import torch
            
            print("📥 Loading SpeechT5...")
            
            self.tts_processor = SpeechT5Processor.from_pretrained("microsoft/speecht5_tts")
            self.tts_model = SpeechT5ForTextToSpeech.from_pretrained("microsoft/speecht5_tts")
            self.tts_vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")
            
            # Load speaker embeddings
            from datasets import load_dataset
            embeddings_dataset = load_dataset("Matthijs/cmu-arctic-xvectors", split="validation")
            self.speaker_embeddings = torch.tensor(embeddings_dataset[7306]["xvector"]).unsqueeze(0)
            
            self.tts_type = "speecht5"
            print("✅ TTS: SpeechT5 (Microsoft)")
            self.tts_available = True
            return
            
        except Exception as e:
            print(f"⚠️ SpeechT5 failed: {e}")
        
        # Option 3: Bark (creative TTS)
        try:
            from transformers import BarkModel, BarkProcessor
            import torch
            
            print("📥 Loading Bark...")
            self.tts_processor = BarkProcessor.from_pretrained("suno/bark-small")
            self.tts_model = BarkModel.from_pretrained("suno/bark-small")
            
            self.tts_type = "bark"
            print("✅ TTS: Bark (creative)")
            self.tts_available = True
            return
            
        except Exception as e:
            print(f"⚠️ Bark failed: {e}")
        
        # Fallback to Edge TTS
        try:
            import edge_tts
            self.tts_type = "edge_tts"
            print("✅ TTS: Edge-TTS (fallback)")
            self.tts_available = True
        except:
            print("❌ No TTS available")
            self.tts_available = False
    
    def _init_modern_vad(self):
        """Initialize modern VAD"""
        try:
            # Try Silero VAD (state-of-the-art)
            import torch
            
            print("📥 Loading Silero VAD...")
            model, utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False
            )
            
            self.vad_model = model
            self.get_speech_timestamps = utils[0]
            self.vad_type = "silero"
            print("✅ VAD: Silero VAD (state-of-the-art)")
            
        except Exception as e:
            print(f"⚠️ Silero VAD failed: {e}")
            self.vad_type = "energy"
            print("✅ VAD: Energy-based fallback")
    
    def detect_speech_modern(self, audio_chunk):
        """Modern speech detection"""
        try:
            if hasattr(self, 'vad_model') and self.vad_type == "silero":
                # Use Silero VAD
                import torch
                audio_tensor = torch.from_numpy(audio_chunk).float()
                speech_prob = self.vad_model(audio_tensor, self.sample_rate).item()
                return speech_prob > 0.5
            else:
                # Energy-based fallback
                energy = np.sqrt(np.mean(audio_chunk ** 2))
                return energy > 0.02
                
        except Exception:
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            return energy > 0.02
    
    def audio_callback(self, indata, frames, time_info, status):
        """Modern audio callback"""
        try:
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            self.audio_queue.put(audio_chunk.copy())
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_modern(self):
        """Modern audio processing"""
        try:
            while not self.audio_queue.empty():
                audio_chunk = self.audio_queue.get_nowait()
                
                has_speech = self.detect_speech_modern(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 User speaking...")
                        
                        # Modern interruption
                        if self.is_ai_speaking:
                            print("⚡ Auto-interrupting AI!")
                            self.stop_speaking = True
                    
                    self.speech_buffer.append(audio_chunk)
                
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 User stopped ({silence_duration:.1f}s)")
                        return True
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_modern(self, audio_data):
        """Modern transcription with latest models"""
        try:
            if not self.stt_available:
                return ""
            
            print("🔄 Transcribing with modern models...")
            
            # Prepare audio
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Normalize
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            if hasattr(self, 'stt_pipeline'):
                # Use modern pipeline
                result = self.stt_pipeline(audio_data)
                return result["text"].strip()
            else:
                # Fallback to faster-whisper
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    import wave
                    with wave.open(tmp_path, 'wb') as wav_file:
                        wav_file.setnchannels(1)
                        wav_file.setsampwidth(2)
                        wav_file.setframerate(self.sample_rate)
                        audio_int16 = (audio_data * 32767).astype(np.int16)
                        wav_file.writeframes(audio_int16.tobytes())
                    
                    segments, _ = self.stt_model.transcribe(tmp_path)
                    return " ".join([segment.text.strip() for segment in segments])
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                        
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_modern_hf(self, text: str):
        """Modern HF TTS with working audio output"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            if self.tts_type == "xtts_v2":
                # XTTS v2 - best quality
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    self.tts_model.tts_to_file(
                        text=text,
                        file_path=tmp_path,
                        speaker_wav=None,
                        language="en"
                    )
                    
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_with_interruption(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            elif self.tts_type == "speecht5":
                # SpeechT5
                import torch
                
                inputs = self.tts_processor(text=text, return_tensors="pt")
                
                with torch.no_grad():
                    speech = self.tts_model.generate_speech(
                        inputs["input_ids"], 
                        self.speaker_embeddings, 
                        vocoder=self.tts_vocoder
                    )
                
                # Convert to numpy and play
                audio_data = speech.numpy()
                await self._play_with_interruption(audio_data, 16000)
            
            elif self.tts_type == "bark":
                # Bark TTS
                import torch
                
                inputs = self.tts_processor(text, voice_preset="v2/en_speaker_6")
                
                with torch.no_grad():
                    audio_array = self.tts_model.generate(**inputs)
                    audio_data = audio_array.cpu().numpy().squeeze()
                
                await self._play_with_interruption(audio_data, 24000)
            
            elif self.tts_type == "edge_tts":
                # Edge TTS fallback
                import edge_tts
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                    await communicate.save(tmp_path)
                    
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_with_interruption(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            else:
                print(f"💬 AI: {text}")
                
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _play_with_interruption(self, audio_data, sample_rate):
        """Play audio with modern interruption"""
        # Ensure correct format
        if len(audio_data.shape) > 1:
            audio_data = audio_data.mean(axis=1)
        
        # Resample if needed
        if sample_rate != self.sample_rate:
            try:
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=self.sample_rate)
                sample_rate = self.sample_rate
            except:
                pass
        
        # Play in chunks for interruption
        chunk_size = int(sample_rate * 0.1)  # 100ms chunks
        
        for i in range(0, len(audio_data), chunk_size):
            if self.stop_speaking:
                print("🛑 Speech interrupted!")
                break
            
            chunk = audio_data[i:i+chunk_size]
            
            # Ensure audio is audible
            if np.max(np.abs(chunk)) > 0:
                # Normalize and play
                chunk = chunk / np.max(np.abs(chunk)) * 0.7  # Prevent clipping
                sd.play(chunk, sample_rate)
                sd.wait()
            
            await asyncio.sleep(0.01)
    
    def generate_response(self, user_input: str) -> str:
        """Generate intelligent responses"""
        responses = {
            "hello": "Hello! I'm using the latest 2025 Hugging Face models for state-of-the-art voice interaction!",
            "hi": "Hi there! This system uses modern HF TTS models and advanced interruption detection.",
            "test": "Testing the modern Hugging Face voice system! I'm using cutting-edge models like XTTS v2, SpeechT5, or Bark for natural speech synthesis. You can interrupt me anytime by speaking!",
            "models": "I'm running on the latest 2025 models: Whisper v3 Turbo for STT, XTTS v2 or SpeechT5 for TTS, and Silero VAD for speech detection.",
            "quality": "The audio quality is exceptional thanks to modern Hugging Face TTS models!",
            "fast": "This system is optimized for speed with the latest model architectures!",
            "interrupt": "Interrupt me anytime! The modern system detects your voice instantly.",
            "quit": "Goodbye! Thanks for trying the modern Hugging Face voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. Processed with modern 2025 HF models!"
    
    async def run_modern_conversation(self):
        """Run modern conversation with latest models"""
        print("\n🚀 Modern HF Voice Conversation 2025")
        print("=" * 50)
        print("🤖 Latest Hugging Face models")
        print("🎯 Modern interruption system")
        print("🔊 High-quality TTS output")
        print("⚡ State-of-the-art performance")
        print("=" * 50)
        
        if not self.stt_available:
            print("❌ STT not available")
            return
        
        try:
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32
            ):
                print("🎤 Modern system ready! Start speaking...")
                
                while True:
                    should_transcribe = self.process_audio_modern()
                    
                    if should_transcribe and len(self.speech_buffer) > 0:
                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()
                        
                        # Modern transcription
                        transcription = await self.transcribe_modern(audio_data)
                        
                        if transcription.strip():
                            print(f"📝 You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye']):
                                print("👋 Modern system shutting down!")
                                break
                            
                            # Generate and speak response
                            response = self.generate_response(transcription)
                            await self.speak_modern_hf(response)
                        else:
                            print("❌ No speech detected")
                    
                    await asyncio.sleep(0.05)
                    
        except KeyboardInterrupt:
            print("\n👋 Modern system stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        await self.run_modern_conversation()

async def main():
    """Main function"""
    print("🚀 Initializing Modern Hugging Face Voice System 2025...")
    voice = ModernHFVoice2025()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
