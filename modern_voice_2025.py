#!/usr/bin/env python3
"""
🎤 MODERN VOICE SYSTEM 2025
Using latest Hugging Face models for ultra-fast, high-quality voice interaction
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
from pathlib import Path

import numpy as np
import torch
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("modern-voice-2025")

class ModernVoice2025:
    """Modern voice system using latest 2025 HF models"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🚀 Using device: {self.device}")
        
        # Models
        self.stt_model = None
        self.tts_model = None
        self.vad_model = None
        
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        
        # State
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue()
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 1.0  # Faster response
        
        print("🎤 Modern Voice System 2025")
        print("=" * 40)
        
        self._init_models()
    
    def _init_models(self):
        """Initialize latest 2025 HF models"""
        print("🔄 Loading latest 2025 models...")
        
        # 1. Ultra-fast STT with Whisper Turbo
        try:
            from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline
            
            print("📥 Loading Whisper Turbo (2024-2025)...")
            model_id = "openai/whisper-large-v3-turbo"
            
            self.stt_processor = AutoProcessor.from_pretrained(model_id)
            self.stt_model = AutoModelForSpeechSeq2Seq.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                low_cpu_mem_usage=True,
                use_safetensors=True
            ).to(self.device)
            
            self.stt_pipeline = pipeline(
                "automatic-speech-recognition",
                model=self.stt_model,
                tokenizer=self.stt_processor.tokenizer,
                feature_extractor=self.stt_processor.feature_extractor,
                max_new_tokens=128,
                chunk_length_s=30,
                batch_size=16,
                return_timestamps=True,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device=self.device,
            )
            
            print("✅ STT: Whisper Turbo loaded")
            
        except Exception as e:
            print(f"❌ STT loading failed: {e}")
            print("📦 Installing required packages...")
            os.system("pip install transformers torch torchaudio accelerate")
            return False
        
        # 2. Modern TTS with Bark or XTTS
        try:
            print("📥 Loading modern TTS...")
            
            # Try XTTS v2 first (best quality)
            try:
                from TTS.api import TTS
                self.tts_model = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(self.device)
                print("✅ TTS: XTTS v2 loaded")
                self.tts_type = "xtts"
            except:
                # Fallback to Bark
                try:
                    from transformers import BarkModel, BarkProcessor
                    self.tts_model = BarkModel.from_pretrained("suno/bark-small").to(self.device)
                    self.tts_processor = BarkProcessor.from_pretrained("suno/bark-small")
                    print("✅ TTS: Bark loaded")
                    self.tts_type = "bark"
                except:
                    # Final fallback to edge-tts
                    import edge_tts
                    print("✅ TTS: Edge-TTS fallback")
                    self.tts_type = "edge"
                    
        except Exception as e:
            print(f"⚠️ TTS loading issue: {e}")
            self.tts_type = "edge"
        
        # 3. Modern VAD with Silero
        try:
            print("📥 Loading Silero VAD...")
            self.vad_model, utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            self.vad_model.to(self.device)
            self.get_speech_timestamps = utils[0]
            print("✅ VAD: Silero VAD loaded")
            
        except Exception as e:
            print(f"⚠️ VAD loading issue: {e}")
            # Fallback to energy-based detection
            self.vad_model = None
        
        print("🚀 All models loaded successfully!")
        return True
    
    def detect_speech(self, audio_chunk):
        """Modern speech detection using Silero VAD"""
        try:
            if self.vad_model is not None:
                # Convert to tensor
                audio_tensor = torch.from_numpy(audio_chunk).float()
                if len(audio_tensor.shape) > 1:
                    audio_tensor = audio_tensor.mean(dim=1)
                
                # Resample if needed (Silero expects 16kHz)
                if len(audio_tensor) > 0:
                    speech_prob = self.vad_model(audio_tensor.unsqueeze(0), self.sample_rate).item()
                    return speech_prob > 0.5
            
            # Fallback to energy detection
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            return energy > 0.01
            
        except Exception as e:
            # Simple energy fallback
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            return energy > 0.01
    
    def audio_callback(self, indata, frames, time_info, status):
        """Real-time audio processing"""
        try:
            # Convert to mono
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            # Queue for main thread processing
            self.audio_queue.put(audio_chunk.copy())
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_stream(self):
        """Process audio stream for speech detection"""
        try:
            while not self.audio_queue.empty():
                audio_chunk = self.audio_queue.get_nowait()
                
                # Detect speech
                has_speech = self.detect_speech(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 User speaking...")
                        
                        # Auto-interrupt AI
                        if self.is_ai_speaking:
                            print("⚡ Auto-interrupting AI!")
                            self.stop_speaking = True
                    
                    # Buffer audio for transcription
                    self.speech_buffer.append(audio_chunk)
                
                # Check for end of speech
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 User stopped ({silence_duration:.1f}s)")
                        return True  # Signal to process speech
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_modern(self, audio_data):
        """Ultra-fast transcription with Whisper Turbo"""
        try:
            print("🔄 Transcribing with Whisper Turbo...")
            
            # Ensure correct format
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Normalize
            audio_data = audio_data.astype(np.float32)
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            # Transcribe with pipeline (much faster)
            result = self.stt_pipeline(
                audio_data,
                return_timestamps=True,
                generate_kwargs={"max_new_tokens": 128}
            )
            
            text = result["text"].strip()
            return text
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_modern(self, text: str):
        """Modern TTS with latest models"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            if self.tts_type == "xtts":
                # XTTS v2 - highest quality
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    # Generate with XTTS
                    self.tts_model.tts_to_file(
                        text=text,
                        file_path=tmp_path,
                        speaker_wav=None,  # Use default voice
                        language="en"
                    )
                    
                    # Load and play
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_audio_chunked(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                        
            elif self.tts_type == "bark":
                # Bark TTS
                inputs = self.tts_processor(text, voice_preset="v2/en_speaker_6")
                
                with torch.no_grad():
                    audio_array = self.tts_model.generate(**inputs.to(self.device))
                    audio_array = audio_array.cpu().numpy().squeeze()
                
                # Play audio
                await self._play_audio_chunked(audio_array, 24000)  # Bark uses 24kHz
                
            else:
                # Edge-TTS fallback
                import edge_tts
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                    await communicate.save(tmp_path)
                    
                    # Load and play
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_audio_chunked(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                        
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _play_audio_chunked(self, audio_data, sample_rate):
        """Play audio in chunks for interruption"""
        # Resample if needed
        if sample_rate != self.sample_rate:
            import librosa
            audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=self.sample_rate)
            sample_rate = self.sample_rate
        
        # Play in small chunks
        chunk_size = int(sample_rate * 0.1)  # 100ms chunks
        
        for i in range(0, len(audio_data), chunk_size):
            if self.stop_speaking:
                print("🛑 Speech interrupted!")
                break
            
            chunk = audio_data[i:i+chunk_size]
            sd.play(chunk, sample_rate)
            sd.wait()
            
            await asyncio.sleep(0.01)
    
    def generate_response(self, user_input: str) -> str:
        """Generate intelligent responses"""
        responses = {
            "hello": "Hello! I'm using the latest 2025 AI models for ultra-fast, high-quality voice interaction!",
            "hi": "Hi there! This modern system uses Whisper Turbo and advanced TTS for the best experience.",
            "test": "This is a test of the modern 2025 voice system. I'm using state-of-the-art Hugging Face models including Whisper Turbo for speech recognition and advanced TTS for natural speech synthesis. You can interrupt me anytime!",
            "how are you": "I'm running on cutting-edge 2025 AI models and feeling fantastic!",
            "fast": "Yes! This system is incredibly fast thanks to Whisper Turbo and optimized model loading.",
            "quality": "The audio quality is excellent with modern TTS models like XTTS v2 and Bark.",
            "interrupt": "Interrupt away! The system detects your voice instantly and stops immediately.",
            "models": "I'm using Whisper Turbo for STT, XTTS v2/Bark for TTS, and Silero VAD for speech detection.",
            "quit": "Goodbye! Thanks for trying the modern 2025 voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. The modern system processed this with lightning speed!"
    
    async def run_conversation(self):
        """Run modern voice conversation"""
        print("\n🚀 Modern Voice Conversation 2025")
        print("=" * 45)
        print("✅ Ultra-fast Whisper Turbo STT")
        print("✅ High-quality modern TTS")
        print("✅ Instant auto-interruption")
        print("✅ Latest Hugging Face models")
        print("=" * 45)
        
        try:
            # Start audio stream
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32
            ):
                print("🎤 Modern system ready! Start speaking...")
                
                while True:
                    # Process audio stream
                    should_transcribe = self.process_audio_stream()
                    
                    if should_transcribe and len(self.speech_buffer) > 0:
                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()
                        
                        # Ultra-fast transcription
                        transcription = await self.transcribe_modern(audio_data)
                        
                        if transcription.strip():
                            print(f"📝 You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye']):
                                print("👋 Modern voice system shutting down!")
                                break
                            
                            # Generate and speak response
                            response = self.generate_response(transcription)
                            await self.speak_modern(response)
                        else:
                            print("❌ No clear speech detected")
                    
                    await asyncio.sleep(0.05)  # Faster processing loop
                    
        except KeyboardInterrupt:
            print("\n👋 Modern voice system stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        if self.stt_model is None:
            print("❌ Models not loaded properly")
            return
        
        await self.run_conversation()

async def main():
    """Main function"""
    print("🚀 Initializing Modern Voice System 2025...")
    voice = ModernVoice2025()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
