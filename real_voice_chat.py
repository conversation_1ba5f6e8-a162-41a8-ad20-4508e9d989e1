#!/usr/bin/env python3
"""
🎤 REAL VOICE CHAT - SPEAK AND HEAR
A real voice chat system that uses your microphone and speakers
"""

import asyncio
import logging
import os
import sys
import tempfile
import time
import wave
import threading
from pathlib import Path

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("real-voice-chat")

class RealVoiceChat:
    """Real voice chat system with microphone and speaker support"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.audio = None
        self.is_listening = False
        self.is_speaking = False
        
        print("🎤 Real Voice Chat System")
        print("=" * 40)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize audio recording
        try:
            import pyaudio
            self.audio = pyaudio.PyAudio()
            print("✅ Audio: pyaudio initialized")
        except ImportError:
            print("❌ Audio: pyaudio not available")
            print("   Install with: pip install pyaudio")
            return False
        
        # Check TTS
        try:
            import edge_tts
            print("✅ TTS: edge-tts available")
        except ImportError:
            print("⚠️ TTS: edge-tts not available")
        
        return True
    
    def list_audio_devices(self):
        """List available audio devices"""
        if not self.audio:
            print("❌ Audio system not initialized")
            return
        
        print("\n🎧 Available Audio Devices:")
        print("-" * 40)
        
        for i in range(self.audio.get_device_count()):
            info = self.audio.get_device_info_by_index(i)
            print(f"{i}: {info['name']} - {info['maxInputChannels']} in, {info['maxOutputChannels']} out")
    
    def record_audio(self, duration=5, sample_rate=16000):
        """Record audio from microphone"""
        if not self.audio:
            print("❌ Audio system not available")
            return None
        
        chunk = 1024
        format = self.audio.get_format_from_width(2)  # 16-bit
        channels = 1
        
        print(f"🎤 Recording for {duration} seconds...")
        print("   Speak now!")
        
        try:
            stream = self.audio.open(
                format=format,
                channels=channels,
                rate=sample_rate,
                input=True,
                frames_per_buffer=chunk
            )
            
            frames = []
            for i in range(0, int(sample_rate / chunk * duration)):
                data = stream.read(chunk)
                frames.append(data)
            
            stream.stop_stream()
            stream.close()
            
            print("✅ Recording complete!")
            
            # Convert to bytes
            audio_data = b''.join(frames)
            return audio_data
            
        except Exception as e:
            print(f"❌ Recording failed: {e}")
            return None
    
    def transcribe_audio(self, audio_data: bytes) -> str:
        """Transcribe audio data to text"""
        if not self.stt_model:
            return ""
        
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(16000)
                    wav_file.writeframes(audio_data)
                
                print("🔄 Transcribing...")
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                # Clean up
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate a response to user input"""
        # Enhanced response system
        responses = {
            "hello": "Hello! I can hear you clearly. How can I help you today?",
            "hi": "Hi there! Great to hear your voice!",
            "how are you": "I'm doing well, thank you for asking! How are you?",
            "what is your name": "I'm your voice assistant. I can hear and speak with you!",
            "goodbye": "Goodbye! It was great talking with you!",
            "bye": "Bye! Have a wonderful day!",
            "test": "Voice system test successful! I can hear you and you should hear me speaking back.",
            "help": "I can have voice conversations with you. Just speak naturally and I'll respond!",
            "can you hear me": "Yes, I can hear you perfectly! Your voice is coming through clearly.",
            "volume": "I'm speaking at a normal volume. Can you hear me clearly?",
            "microphone": "Your microphone is working well! I can hear you clearly.",
            "speaker": "If you can hear this, your speakers are working correctly!",
        }
        
        # Simple keyword matching
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        # Default response
        return f"I heard you say: '{user_input}'. That's interesting! Tell me more."
    
    async def speak_text(self, text: str):
        """Convert text to speech and play it"""
        print(f"🔊 Speaking: {text}")
        
        try:
            import edge_tts
            
            # Create TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                await communicate.save(tmp_path)
                
                # Try different audio players
                success = False
                
                # Try pygame
                try:
                    import pygame
                    pygame.mixer.init()
                    pygame.mixer.music.load(tmp_path)
                    pygame.mixer.music.play()
                    
                    # Wait for playback to finish
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)
                    
                    success = True
                    
                except ImportError:
                    pass
                
                # Try playsound as fallback
                if not success:
                    try:
                        import playsound
                        playsound.playsound(tmp_path)
                        success = True
                    except ImportError:
                        pass
                
                # Try system command as last resort
                if not success:
                    try:
                        import subprocess
                        if sys.platform == "win32":
                            subprocess.run(["start", tmp_path], shell=True, check=True)
                        elif sys.platform == "darwin":
                            subprocess.run(["afplay", tmp_path], check=True)
                        else:
                            subprocess.run(["aplay", tmp_path], check=True)
                        
                        # Wait a bit for playback
                        await asyncio.sleep(len(text) * 0.1)  # Rough estimate
                        success = True
                    except:
                        pass
                
                if not success:
                    print("⚠️ Could not play audio. Install pygame or playsound:")
                    print("   pip install pygame")
                    print("   pip install playsound")
                    
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except ImportError:
            # Fallback to text output
            print(f"💬 Assistant: {text}")
            print("   (Install edge-tts for voice output: pip install edge-tts)")
    
    async def voice_conversation_loop(self):
        """Main voice conversation loop"""
        print("\n🎙️ Voice Conversation Mode")
        print("-" * 40)
        print("Press Enter to start recording, or type 'quit' to exit")
        
        conversation_count = 0
        
        while True:
            try:
                # Get user input
                user_input = input(f"\n[{conversation_count + 1}] Press Enter to record (or type 'quit'): ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    await self.speak_text("Goodbye! It was great talking with you!")
                    break
                
                if user_input:
                    # User typed something
                    transcribed_text = user_input
                    print(f"📝 You typed: '{transcribed_text}'")
                else:
                    # Record audio
                    audio_data = self.record_audio(duration=5)
                    if not audio_data:
                        continue
                    
                    # Transcribe
                    transcribed_text = self.transcribe_audio(audio_data)
                    if not transcribed_text.strip():
                        print("❌ Could not understand audio. Try speaking louder or closer to microphone.")
                        continue
                    
                    print(f"📝 You said: '{transcribed_text}'")
                
                # Generate response
                response = self.generate_response(transcribed_text)
                
                # Speak response
                await self.speak_text(response)
                
                conversation_count += 1
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
    
    def show_menu(self):
        """Show main menu"""
        print("\n🎤 Real Voice Chat System")
        print("=" * 40)
        print("1. Test Audio Devices")
        print("2. Start Voice Conversation")
        print("3. Test Recording")
        print("4. Test Text-to-Speech")
        print("5. Exit")
        print("-" * 40)
        
        choice = input("Select option (1-5): ").strip()
        return choice
    
    async def test_recording(self):
        """Test audio recording"""
        print("\n🎤 Testing Audio Recording")
        print("-" * 40)
        
        audio_data = self.record_audio(duration=3)
        if audio_data:
            transcribed = self.transcribe_audio(audio_data)
            print(f"📝 Transcribed: '{transcribed}'")
            
            if transcribed.strip():
                print("✅ Recording and transcription working!")
            else:
                print("⚠️ No speech detected. Try speaking louder.")
        else:
            print("❌ Recording failed")
    
    async def test_tts(self):
        """Test text-to-speech"""
        print("\n🔊 Testing Text-to-Speech")
        print("-" * 40)
        
        test_text = "Hello! This is a test of the text to speech system. Can you hear me clearly?"
        await self.speak_text(test_text)
    
    async def run(self):
        """Main run loop"""
        if not self.stt_model or not self.vad or not self.audio:
            print("❌ System not properly initialized")
            print("\nMissing dependencies? Install with:")
            print("   pip install pyaudio faster-whisper webrtcvad edge-tts pygame")
            return
        
        while True:
            choice = self.show_menu()
            
            if choice == "1":
                self.list_audio_devices()
            elif choice == "2":
                await self.voice_conversation_loop()
            elif choice == "3":
                await self.test_recording()
            elif choice == "4":
                await self.test_tts()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-5.")
            
            input("\nPress Enter to continue...")

async def main():
    """Main function"""
    chat = RealVoiceChat()
    await chat.run()

if __name__ == "__main__":
    asyncio.run(main())
