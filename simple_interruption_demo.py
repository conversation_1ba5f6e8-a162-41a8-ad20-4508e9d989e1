#!/usr/bin/env python3
"""
🎤 SIMPLE INTERRUPTION DEMO
A working demonstration of voice interruption concepts
"""

import asyncio
import logging
import os
import tempfile
import wave
from datetime import datetime

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("interruption-demo")

class SimpleInterruptionDemo:
    """Simple demonstration of interruption concepts"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.sample_rate = 16000
        self.is_ai_speaking = False
        self.stop_speaking = False
        
        print("🎤 Simple Interruption Demo")
        print("=" * 40)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize audio
        try:
            import sounddevice as sd
            print("✅ Audio: sounddevice available")
        except ImportError:
            print("❌ Audio: sounddevice not available")
            return False
        
        return True
    
    def record_with_vad(self, max_duration=10):
        """Record audio with voice activity detection"""
        try:
            import sounddevice as sd
            
            print(f"🎤 Recording with smart silence detection...")
            print("   Speak naturally, including pauses")
            print("   I'll wait 2 seconds after you stop before responding")
            
            audio_buffer = []
            silence_threshold = 2.0  # 2 seconds of silence
            last_speech_time = None
            recording = True
            
            def audio_callback(indata, frames, time, status):
                nonlocal last_speech_time, recording
                
                if status:
                    print(f"Audio status: {status}")
                
                # Convert to mono
                if len(indata.shape) > 1:
                    audio_chunk = indata[:, 0]
                else:
                    audio_chunk = indata.flatten()
                
                # Add to buffer
                audio_buffer.append(audio_chunk.copy())
                
                # Detect voice activity
                has_speech = self.detect_voice_activity(audio_chunk)
                
                if has_speech:
                    last_speech_time = datetime.now()
                    print("🗣️", end="", flush=True)  # Visual feedback
                elif last_speech_time:
                    # Check silence duration
                    silence_duration = (datetime.now() - last_speech_time).total_seconds()
                    if silence_duration > silence_threshold:
                        print(f"\n🔇 Detected {silence_duration:.1f}s silence - stopping recording")
                        recording = False
                        return
            
            # Start recording
            with sd.InputStream(
                callback=audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                dtype=np.float32
            ):
                start_time = datetime.now()
                while recording:
                    # Check max duration
                    if (datetime.now() - start_time).total_seconds() > max_duration:
                        print(f"\n⏰ Max duration ({max_duration}s) reached")
                        break
                    
                    # Small sleep to prevent busy waiting
                    import time
                    time.sleep(0.1)
            
            if audio_buffer:
                # Combine all audio chunks
                audio_data = np.concatenate(audio_buffer)
                return audio_data
            else:
                return None
                
        except Exception as e:
            print(f"❌ Recording error: {e}")
            return None
    
    def detect_voice_activity(self, audio_chunk):
        """Detect voice activity in audio chunk"""
        try:
            # Convert to int16 for VAD
            if audio_chunk.dtype != np.int16:
                audio_chunk = (audio_chunk * 32767).astype(np.int16)
            
            # VAD requires specific frame sizes (10, 20, or 30ms)
            frame_size = 480  # 30ms at 16kHz
            if len(audio_chunk) >= frame_size:
                frame = audio_chunk[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            return False
            
        except Exception:
            # Fallback to energy detection
            energy = np.sqrt(np.mean(audio_chunk.astype(np.float32) ** 2))
            return energy > 0.01
    
    async def transcribe_audio(self, audio_data):
        """Transcribe audio data"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    # Convert to int16
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                print("🔄 Transcribing...")
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate response"""
        responses = {
            "hello": "Hello! I waited until you were completely finished speaking before responding.",
            "test": "This is a test response. I'm speaking now, and you can interrupt me at any time by pressing Enter and speaking again. The interruption system allows for natural conversation flow.",
            "long": "This is a very long response to demonstrate the interruption system. I will keep talking for a while so you can test interrupting me. The system is designed to handle natural conversation patterns where people might interrupt each other. You can stop me at any time by speaking again.",
            "interrupt": "Yes! You can interrupt me anytime. Just start recording again while I'm speaking and I'll stop immediately.",
            "quit": "Goodbye! The interruption demo is complete.",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. I waited for your complete message before responding!"
    
    async def speak_with_interruption(self, text: str):
        """Speak text with interruption capability"""
        print(f"🔊 AI: {text}")
        print("   (Press Enter to interrupt)")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            import edge_tts
            import sounddevice as sd
            import soundfile as sf
            
            # Generate TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Save audio
                await communicate.save(tmp_path.replace('.wav', '.mp3'))
                
                # Load audio
                data, fs = sf.read(tmp_path.replace('.wav', '.mp3'))
                
                # Play in chunks to allow interruption
                chunk_size = int(fs * 0.2)  # 200ms chunks
                
                for i in range(0, len(data), chunk_size):
                    if self.stop_speaking:
                        print("🛑 Speech interrupted!")
                        break
                    
                    chunk = data[i:i+chunk_size]
                    sd.play(chunk, fs)
                    sd.wait()
                    
                    # Check for interruption signal
                    await asyncio.sleep(0.01)
                
            finally:
                # Cleanup
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except (OSError, PermissionError):
                        pass
                        
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
    
    async def demo_conversation(self):
        """Run the interruption demo"""
        print("\n🎙️ Interruption Demo - How It Works")
        print("=" * 50)
        print("1. 🎤 SMART RECORDING: Speak naturally with pauses")
        print("2. 🔇 SILENCE DETECTION: I wait 2s after you stop")
        print("3. 🔊 CHUNKED SPEECH: I speak in small pieces")
        print("4. ⚡ INTERRUPTION: Press Enter while I speak to interrupt")
        print("=" * 50)
        
        conversation_count = 0
        
        while True:
            try:
                print(f"\n--- Turn {conversation_count + 1} ---")
                
                if self.is_ai_speaking:
                    # Allow interruption
                    input("Press Enter to interrupt AI...")
                    self.stop_speaking = True
                    print("⚠️ Interrupting AI speech!")
                    continue
                
                # Record user speech with smart silence detection
                audio_data = self.record_with_vad(max_duration=15)
                
                if audio_data is None or len(audio_data) == 0:
                    print("❌ No audio recorded")
                    continue
                
                # Transcribe
                transcription = await self.transcribe_audio(audio_data)
                
                if not transcription.strip():
                    print("❌ No speech detected")
                    continue
                
                print(f"📝 You said: '{transcription}'")
                
                # Check for quit
                if 'quit' in transcription.lower():
                    print("👋 Demo complete!")
                    break
                
                # Generate and speak response
                response = self.generate_response(transcription)
                await self.speak_with_interruption(response)
                
                conversation_count += 1
                
            except KeyboardInterrupt:
                print("\n👋 Demo interrupted!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def run(self):
        """Main run function"""
        if not self.stt_model or not self.vad:
            print("❌ System not properly initialized")
            return
        
        await self.demo_conversation()

async def main():
    """Main function"""
    demo = SimpleInterruptionDemo()
    await demo.run()

if __name__ == "__main__":
    asyncio.run(main())
