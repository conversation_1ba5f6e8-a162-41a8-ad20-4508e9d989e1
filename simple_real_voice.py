#!/usr/bin/env python3
"""
🎤 SIMPLE REAL VOICE CHAT
A simple voice chat system using sounddevice for audio
"""

import asyncio
import logging
import os
import sys
import tempfile
import time
import wave
from pathlib import Path

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple-real-voice")

class SimpleRealVoice:
    """Simple real voice chat using sounddevice"""
    
    def __init__(self):
        self.stt_model = None
        self.sample_rate = 16000
        
        print("🎤 Simple Real Voice Chat")
        print("=" * 40)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Check sounddevice
        try:
            import sounddevice as sd
            print("✅ Audio: sounddevice available")
            print(f"   Default input device: {sd.default.device[0]}")
            print(f"   Default output device: {sd.default.device[1]}")
        except ImportError:
            print("❌ Audio: sounddevice not available")
            return False
        
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Check TTS
        try:
            import edge_tts
            print("✅ TTS: edge-tts available")
        except ImportError:
            print("⚠️ TTS: edge-tts not available")
        
        return True
    
    def list_audio_devices(self):
        """List available audio devices"""
        try:
            import sounddevice as sd
            print("\n🎧 Available Audio Devices:")
            print("-" * 50)
            print(sd.query_devices())
        except ImportError:
            print("❌ sounddevice not available")
    
    def record_audio(self, duration=5):
        """Record audio using sounddevice"""
        try:
            import sounddevice as sd
            
            print(f"🎤 Recording for {duration} seconds...")
            print("   Speak now!")
            
            # Record audio
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.int16
            )
            sd.wait()  # Wait for recording to complete
            
            print("✅ Recording complete!")
            
            # Convert to bytes
            audio_bytes = audio_data.tobytes()
            return audio_bytes
            
        except ImportError:
            print("❌ sounddevice not available")
            return None
        except Exception as e:
            print(f"❌ Recording failed: {e}")
            return None
    
    def transcribe_audio(self, audio_data: bytes) -> str:
        """Transcribe audio data to text"""
        if not self.stt_model:
            return ""
        
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)
                
                print("🔄 Transcribing...")
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                # Clean up
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate a response to user input"""
        responses = {
            "hello": "Hello! I can hear you clearly through your microphone!",
            "hi": "Hi there! Your voice is coming through perfectly!",
            "how are you": "I'm doing great! I can hear you speaking to me.",
            "what is your name": "I'm your voice assistant. I can hear your voice and speak back to you!",
            "goodbye": "Goodbye! It was wonderful hearing your voice!",
            "test": "Voice test successful! I heard you clearly and I'm speaking back to you now.",
            "can you hear me": "Yes! I can hear you perfectly through your microphone!",
            "microphone": "Your microphone is working excellently! I can hear you clearly.",
            "speaker": "If you can hear my voice, your speakers are working perfectly!",
            "volume": "I'm speaking at normal volume. Can you hear me clearly?",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard you say: '{user_input}'. Your voice came through clearly!"
    
    async def speak_text(self, text: str):
        """Convert text to speech"""
        print(f"🔊 Speaking: {text}")
        
        try:
            import edge_tts
            import sounddevice as sd
            import soundfile as sf
            
            # Create TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Save as WAV for better compatibility
                await communicate.save(tmp_path.replace('.wav', '.mp3'))
                
                # Convert MP3 to WAV if needed, or try to play MP3 directly
                try:
                    # Try to read and play the audio file
                    data, fs = sf.read(tmp_path.replace('.wav', '.mp3'))
                    sd.play(data, fs)
                    sd.wait()  # Wait for playback to complete
                    
                except Exception as e:
                    # Fallback: just print the text
                    print(f"💬 Assistant: {text}")
                    print(f"   (Audio playback failed: {e})")
                    
            finally:
                # Clean up
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except (OSError, PermissionError):
                        pass
                        
        except ImportError as e:
            print(f"💬 Assistant: {text}")
            print(f"   (Missing audio libraries: {e})")
        except Exception as e:
            print(f"💬 Assistant: {text}")
            print(f"   (TTS error: {e})")
    
    async def voice_conversation(self):
        """Simple voice conversation"""
        print("\n🎙️ Voice Conversation")
        print("-" * 40)
        print("Press Enter to record your voice, or type 'quit' to exit")
        
        conversation_count = 0
        
        while True:
            try:
                user_input = input(f"\n[{conversation_count + 1}] Press Enter to speak (or 'quit'): ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    await self.speak_text("Goodbye! Thanks for testing the voice system!")
                    break
                
                if user_input:
                    # User typed something
                    transcribed_text = user_input
                    print(f"📝 You typed: '{transcribed_text}'")
                else:
                    # Record voice
                    audio_data = self.record_audio(duration=4)
                    if not audio_data:
                        continue
                    
                    # Transcribe
                    transcribed_text = self.transcribe_audio(audio_data)
                    if not transcribed_text.strip():
                        print("❌ No speech detected. Try speaking louder or closer to the microphone.")
                        continue
                    
                    print(f"📝 You said: '{transcribed_text}'")
                
                # Generate and speak response
                response = self.generate_response(transcribed_text)
                await self.speak_text(response)
                
                conversation_count += 1
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def test_microphone(self):
        """Test microphone recording"""
        print("\n🎤 Testing Microphone")
        print("-" * 40)
        
        audio_data = self.record_audio(duration=3)
        if audio_data:
            transcribed = self.transcribe_audio(audio_data)
            if transcribed.strip():
                print(f"✅ Success! I heard: '{transcribed}'")
                await self.speak_text(f"Great! I heard you say: {transcribed}")
            else:
                print("⚠️ No speech detected. Check your microphone.")
        else:
            print("❌ Recording failed")
    
    async def test_speakers(self):
        """Test speaker output"""
        print("\n🔊 Testing Speakers")
        print("-" * 40)
        
        test_message = "Hello! This is a test of your speakers. Can you hear me clearly?"
        await self.speak_text(test_message)
    
    def show_menu(self):
        """Show menu"""
        print("\n🎤 Simple Real Voice Chat")
        print("=" * 40)
        print("1. List Audio Devices")
        print("2. Test Microphone")
        print("3. Test Speakers")
        print("4. Start Voice Conversation")
        print("5. Exit")
        print("-" * 40)
        
        return input("Select option (1-5): ").strip()
    
    async def run(self):
        """Main run loop"""
        # Check if components are available
        try:
            import sounddevice
        except ImportError:
            print("❌ sounddevice not available. Install with:")
            print("   pip install sounddevice soundfile")
            return
        
        if not self.stt_model:
            print("❌ STT not available")
            return
        
        print("✅ System ready for voice conversations!")
        
        while True:
            choice = self.show_menu()
            
            if choice == "1":
                self.list_audio_devices()
            elif choice == "2":
                await self.test_microphone()
            elif choice == "3":
                await self.test_speakers()
            elif choice == "4":
                await self.voice_conversation()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")
            
            input("\nPress Enter to continue...")

async def main():
    """Main function"""
    voice = SimpleRealVoice()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
