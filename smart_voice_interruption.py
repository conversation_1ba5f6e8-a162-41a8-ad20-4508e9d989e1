#!/usr/bin/env python3
"""
🎤 SMART VOICE INTERRUPTION SYSTEM
Advanced voice chat with intelligent turn-taking and interruption handling
"""

import asyncio
import logging
import os
import sys
import tempfile
import time
import wave
import threading
from collections import deque
from pathlib import Path

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("smart-voice")

class SmartVoiceInterruption:
    """Smart voice system with interruption and turn-taking"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.sample_rate = 16000
        self.chunk_size = 1024
        
        # Voice activity tracking
        self.is_user_speaking = False
        self.is_ai_speaking = False
        self.speech_buffer = deque(maxlen=50)  # Last 50 audio chunks
        self.silence_threshold = 1.5  # Seconds of silence before AI responds
        self.last_speech_time = 0
        
        # Audio streaming
        self.audio_stream = None
        self.stop_listening = False
        self.stop_speaking = False
        
        print("🎤 Smart Voice Interruption System")
        print("=" * 50)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)  # Aggressiveness level 2
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize audio
        try:
            import sounddevice as sd
            print("✅ Audio: sounddevice available")
            print(f"   Input device: {sd.default.device[0]}")
            print(f"   Output device: {sd.default.device[1]}")
        except ImportError:
            print("❌ Audio: sounddevice not available")
            return False
        
        return True
    
    def detect_voice_activity(self, audio_chunk):
        """Detect if there's voice activity in audio chunk"""
        try:
            # Convert to int16 if needed
            if audio_chunk.dtype != np.int16:
                audio_chunk = (audio_chunk * 32767).astype(np.int16)
            
            # Ensure correct size for VAD (30ms = 480 samples at 16kHz)
            frame_size = 480
            if len(audio_chunk) >= frame_size:
                frame = audio_chunk[:frame_size].tobytes()
                return self.vad.is_speech(frame, self.sample_rate)
            return False
            
        except Exception as e:
            # Fallback to energy-based detection
            energy = np.sqrt(np.mean(audio_chunk.astype(np.float32) ** 2))
            return energy > 0.01  # Adjust threshold as needed
    
    def audio_callback(self, indata, frames, time_info, status):
        """Real-time audio callback for continuous monitoring"""
        if status:
            print(f"Audio callback status: {status}")

        # Convert to mono if stereo
        if len(indata.shape) > 1:
            audio_chunk = indata[:, 0]
        else:
            audio_chunk = indata.flatten()

        # Detect voice activity
        has_speech = self.detect_voice_activity(audio_chunk)

        # Update speech tracking
        import time
        current_time = time.time()
        if has_speech:
            self.last_speech_time = current_time
            if not self.is_user_speaking:
                self.is_user_speaking = True
                print("🎤 User started speaking...")

                # Interrupt AI if it's speaking
                if self.is_ai_speaking:
                    print("⚠️ Interrupting AI...")
                    self.stop_speaking = True

        # Add to buffer for later transcription
        self.speech_buffer.append(audio_chunk.copy())

        # Check if user stopped speaking (silence detection)
        silence_duration = current_time - self.last_speech_time
        if self.is_user_speaking and silence_duration > self.silence_threshold:
            self.is_user_speaking = False
            print(f"🔇 User stopped speaking (silence: {silence_duration:.1f}s)")

            # Trigger transcription and response
            try:
                asyncio.create_task(self.process_user_speech())
            except RuntimeError:
                # Handle case where event loop is not running
                pass
    
    async def process_user_speech(self):
        """Process accumulated user speech"""
        if len(self.speech_buffer) == 0:
            return
        
        print("🔄 Processing user speech...")
        
        # Combine audio chunks
        audio_data = np.concatenate(list(self.speech_buffer))
        
        # Clear buffer
        self.speech_buffer.clear()
        
        # Transcribe
        transcription = await self.transcribe_audio_async(audio_data)
        
        if transcription.strip():
            print(f"📝 You said: '{transcription}'")
            
            # Generate and speak response
            response = self.generate_response(transcription)
            await self.speak_with_interruption(response)
        else:
            print("❌ No speech detected in audio")
    
    async def transcribe_audio_async(self, audio_data):
        """Transcribe audio data asynchronously"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    # Convert to int16
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                # Transcribe in thread to avoid blocking
                loop = asyncio.get_event_loop()
                segments, _ = await loop.run_in_executor(
                    None, self.stt_model.transcribe, tmp_path
                )
                
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate response to user input"""
        responses = {
            "hello": "Hello! I'm listening carefully and won't interrupt you.",
            "hi": "Hi there! I'll wait until you're completely finished speaking.",
            "test": "This is a test of the interruption system. You can interrupt me at any time by speaking!",
            "interrupt": "Yes, you can interrupt me anytime! Just start speaking and I'll stop immediately.",
            "how are you": "I'm doing well! I'm designed to have natural conversations with proper turn-taking.",
            "goodbye": "Goodbye! The interruption system worked perfectly during our conversation.",
            "long": "This is a longer response to test the interruption system. I'm speaking for a while so you can try interrupting me. Keep talking and see how quickly I stop when you start speaking. The system should detect your voice immediately and stop my speech.",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard you say: '{user_input}'. I waited until you were completely done speaking before responding!"
    
    async def speak_with_interruption(self, text: str):
        """Speak text but allow interruption"""
        print(f"🔊 AI speaking: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            import edge_tts
            import sounddevice as sd
            import soundfile as sf
            
            # Create TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Generate audio
                await communicate.save(tmp_path.replace('.wav', '.mp3'))
                
                # Load audio data
                data, fs = sf.read(tmp_path.replace('.wav', '.mp3'))
                
                # Play in chunks to allow interruption
                chunk_size = int(fs * 0.1)  # 100ms chunks
                
                for i in range(0, len(data), chunk_size):
                    # Check for interruption
                    if self.stop_speaking:
                        print("🛑 AI speech interrupted!")
                        break
                    
                    # Play chunk
                    chunk = data[i:i+chunk_size]
                    sd.play(chunk, fs)
                    sd.wait()
                    
                    # Small delay to check for interruption
                    await asyncio.sleep(0.01)
                
            finally:
                # Clean up
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except (OSError, PermissionError):
                        pass
                        
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def start_continuous_listening(self):
        """Start continuous audio monitoring"""
        try:
            import sounddevice as sd

            print("\n🎙️ Starting Continuous Voice Monitoring")
            print("=" * 50)
            print("✅ Speak naturally - I'll wait for you to finish")
            print("✅ You can interrupt me anytime by speaking")
            print("✅ Press Ctrl+C to exit")
            print("=" * 50)

            # Start audio stream
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32
            ):
                print("🎤 Listening... Start speaking!")

                # Keep running until interrupted
                try:
                    while not self.stop_listening:
                        await asyncio.sleep(0.1)
                except KeyboardInterrupt:
                    print("\n👋 Stopping...")

        except Exception as e:
            print(f"❌ Audio streaming error: {e}")
            print("Try: pip install sounddevice soundfile")
    
    def show_instructions(self):
        """Show usage instructions"""
        print("\n📋 How the Smart Interruption System Works:")
        print("=" * 60)
        print("🎤 USER SPEAKING:")
        print("   • Speak naturally, including pauses")
        print("   • System waits 1.5 seconds of silence before responding")
        print("   • No need to press buttons or wait for prompts")
        print()
        print("🔊 AI SPEAKING:")
        print("   • AI speaks its response")
        print("   • You can interrupt anytime by speaking")
        print("   • AI stops immediately when you start talking")
        print()
        print("🔄 TURN-TAKING:")
        print("   • Natural conversation flow")
        print("   • No awkward overlaps or interruptions")
        print("   • Smart silence detection")
        print()
        print("🧪 TEST PHRASES:")
        print("   • 'test' - Get a long response to practice interrupting")
        print("   • 'long' - Even longer response for interruption testing")
        print("   • 'interrupt' - Learn about interruption features")
        print("   • 'quit' - Exit the system")
        print("=" * 60)
    
    async def run(self):
        """Main run function"""
        if not self.stt_model or not self.vad:
            print("❌ System not properly initialized")
            return
        
        self.show_instructions()
        
        # Start continuous listening
        await self.start_continuous_listening()

async def main():
    """Main function"""
    system = SmartVoiceInterruption()
    await system.run()

if __name__ == "__main__":
    asyncio.run(main())
