#!/usr/bin/env python3
"""test_advanced_features.py

Comprehensive test suite for the enhanced Voice AI system.
Tests streaming TTS, barge-in, GPU detection, and error handling.
"""

import asyncio
import logging
import time
import numpy as np
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_gpu_detection():
    """Test GPU auto-detection for STT."""
    print("🧪 Testing GPU Detection...")
    
    from voice_ai.app import VoiceAI
    
    app = VoiceAI()
    cuda_available = app._cuda_available()
    
    print(f"   CUDA Available: {cuda_available}")
    
    if cuda_available:
        print("   ✅ GPU detected - will use CUDA acceleration")
    else:
        print("   ✅ No GPU - will use CPU (expected on most systems)")
    
    return True

async def test_streaming_tts():
    """Test streaming TTS implementation."""
    print("🧪 Testing Streaming TTS...")
    
    try:
        import edge_tts
        
        # Test streaming capability
        communicate = edge_tts.Communicate(
            "This is a test of streaming text to speech.",
            "en-US-AriaNeural"
        )
        
        chunks = []
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                chunks.append(chunk["data"])
        
        total_audio = b''.join(chunks)
        
        print(f"   ✅ Streaming TTS working - generated {len(total_audio)} bytes")
        print(f"   ✅ Audio chunks: {len(chunks)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Streaming TTS failed: {e}")
        return False

async def test_cancellation_support():
    """Test cancellation and barge-in functionality."""
    print("🧪 Testing Cancellation Support...")
    
    from voice_ai.app import VoiceAI
    
    app = VoiceAI()
    
    # Test stop event
    app.stop_event.clear()
    assert not app.stop_event.is_set(), "Stop event should start clear"
    
    app.request_stop()
    assert app.stop_event.is_set(), "Stop event should be set after request_stop()"
    
    print("   ✅ Stop event mechanism working")
    
    # Test cancellation during mock operation
    async def mock_long_operation():
        for i in range(10):
            if app.stop_event.is_set():
                raise asyncio.CancelledError("Operation cancelled")
            await asyncio.sleep(0.1)
        return "completed"
    
    app.stop_event.clear()
    
    # Start operation and cancel it
    task = asyncio.create_task(mock_long_operation())
    await asyncio.sleep(0.2)  # Let it run briefly
    app.request_stop()
    
    try:
        result = await task
        print("   ❌ Task should have been cancelled")
        return False
    except asyncio.CancelledError:
        print("   ✅ Task cancellation working")
        return True

async def test_audio_backend_selection():
    """Test audio backend selection and lazy initialization."""
    print("🧪 Testing Audio Backend Selection...")
    
    from voice_ai.audio.player import AUDIO_BACKEND, _ensure_pygame
    
    print(f"   Selected backend: {AUDIO_BACKEND}")
    
    if AUDIO_BACKEND == "pygame":
        # Test lazy initialization
        import pygame
        
        # Should not be initialized yet
        mixer_init = pygame.mixer.get_init()
        print(f"   Mixer init before: {mixer_init}")
        
        # Initialize lazily
        _ensure_pygame()
        
        mixer_init_after = pygame.mixer.get_init()
        print(f"   Mixer init after: {mixer_init_after is not None}")
        
        print("   ✅ Lazy pygame initialization working")
        
        # Clean up
        pygame.mixer.quit()
        
    return True

async def test_error_recovery():
    """Test error recovery and restart capability."""
    print("🧪 Testing Error Recovery...")
    
    from voice_ai.ui.cli import VoiceCLI
    
    cli = VoiceCLI()
    
    # Test that CLI can handle initialization failure gracefully
    class MockVoiceAI:
        async def initialize(self):
            raise Exception("Mock initialization failure")
    
    # Temporarily replace VoiceAI
    original_init = cli.initialize
    
    async def mock_init():
        try:
            mock_app = MockVoiceAI()
            await mock_app.initialize()
            return True
        except Exception as e:
            logger.error("Mock error: %s", e)
            return False
    
    cli.initialize = mock_init
    
    result = await cli.initialize()
    assert not result, "Should return False on initialization failure"
    
    print("   ✅ Error recovery working")
    
    # Restore original
    cli.initialize = original_init
    
    return True

async def test_configuration_loading():
    """Test configuration system."""
    print("🧪 Testing Configuration Loading...")
    
    from voice_ai.config import settings
    
    # Check that settings are loaded
    assert hasattr(settings, 'audio'), "Audio settings should be available"
    assert hasattr(settings, 'stt'), "STT settings should be available"
    assert hasattr(settings, 'tts'), "TTS settings should be available"
    assert hasattr(settings, 'llm'), "LLM settings should be available"
    assert hasattr(settings, 'performance'), "Performance settings should be available"
    
    print(f"   Audio sample rate: {settings.audio.sample_rate}")
    print(f"   STT model: {settings.stt.model_size}")
    print(f"   TTS voice: {settings.tts.voice}")
    print(f"   LLM model: {settings.llm.fast_model}")
    
    print("   ✅ Configuration system working")
    
    return True

async def test_performance_monitoring():
    """Test performance monitoring capabilities."""
    print("🧪 Testing Performance Monitoring...")
    
    try:
        # Import and run performance monitor
        import performance_monitor
        
        monitor = performance_monitor.PerformanceMonitor()
        
        # Run a quick check
        start_time = time.time()
        metrics = await monitor.run_comprehensive_check()
        duration = time.time() - start_time
        
        print(f"   Analysis completed in {duration:.2f}s")
        
        # Check that we got metrics
        assert "system" in metrics, "System metrics should be available"
        assert "ollama" in metrics, "Ollama metrics should be available"
        assert "audio" in metrics, "Audio metrics should be available"
        assert "recommendations" in metrics, "Recommendations should be available"
        
        print(f"   System CPU: {metrics['system'].get('cpu_usage_percent', 0):.1f}%")
        print(f"   Available backends: {len([k for k, v in metrics['audio'].items() if v.get('available')])}")
        print(f"   Recommendations: {len(metrics['recommendations'])}")
        
        print("   ✅ Performance monitoring working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Performance monitoring failed: {e}")
        return False

async def run_all_tests():
    """Run all advanced feature tests."""
    print("🚀 ADVANCED FEATURES TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("GPU Detection", test_gpu_detection),
        ("Streaming TTS", test_streaming_tts),
        ("Cancellation Support", test_cancellation_support),
        ("Audio Backend Selection", test_audio_backend_selection),
        ("Error Recovery", test_error_recovery),
        ("Configuration Loading", test_configuration_loading),
        ("Performance Monitoring", test_performance_monitoring),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ ERROR - {test_name}: {e}")
        
        print()
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print("=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Advanced features are working correctly")
    else:
        print("⚠️ Some tests failed - check output above")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests()) 