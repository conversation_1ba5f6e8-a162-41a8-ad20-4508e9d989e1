#!/usr/bin/env python3
"""
Quick test to verify all voice system fixes
"""

import time
import numpy as np

def test_stt_performance():
    """Test STT performance with optimized settings"""
    print("🎤 Testing STT Performance Fix...")
    
    try:
        from faster_whisper import WhisperModel
        
        # Test original settings (slow)
        print("Testing original settings...")
        start_time = time.time()
        model_orig = WhisperModel("tiny.en", device="cpu")
        dummy_audio = np.zeros(16000 * 3, dtype=np.float32)  # 3 seconds
        segments, _ = model_orig.transcribe(dummy_audio)
        list(segments)
        orig_time = time.time() - start_time
        orig_rtf = orig_time / 3.0
        
        # Test optimized settings (fast)
        print("Testing optimized settings...")
        start_time = time.time()
        model_opt = WhisperModel(
            "tiny.en", 
            device="cpu", 
            compute_type="int8",
            cpu_threads=1,
            num_workers=1
        )
        segments, _ = model_opt.transcribe(
            dummy_audio,
            beam_size=1,
            temperature=0.0,
            word_timestamps=False,
            vad_filter=False,
            language="en"
        )
        list(segments)
        opt_time = time.time() - start_time
        opt_rtf = opt_time / 3.0
        
        print(f"Original RTF: {orig_rtf:.3f}")
        print(f"Optimized RTF: {opt_rtf:.3f}")
        print(f"Improvement: {((orig_rtf - opt_rtf) / orig_rtf * 100):.1f}%")
        print(f"Target met: {'✅ YES' if opt_rtf < 0.35 else '❌ NO'}")
        
        return opt_rtf < 0.35
        
    except ImportError:
        print("❌ faster-whisper not available")
        return False
    except Exception as e:
        print(f"❌ STT test failed: {e}")
        return False

def test_tts_engines():
    """Test TTS engine availability"""
    print("\n🔊 Testing TTS Engine Fixes...")
    
    engines_working = 0
    
    # Test Edge TTS
    try:
        import edge_tts
        print("✅ Edge TTS: Available")
        engines_working += 1
    except ImportError:
        print("❌ Edge TTS: Not available")
    
    # Test Windows SAPI
    import sys
    import subprocess
    if sys.platform == "win32":
        try:
            cmd = [
                'powershell', '-Command', 
                'Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Speak("test")'
            ]
            result = subprocess.run(cmd, capture_output=True, timeout=5, text=True)
            if result.returncode == 0:
                print("✅ Windows SAPI: Available")
                engines_working += 1
            else:
                print("❌ Windows SAPI: Failed")
        except Exception as e:
            print(f"❌ Windows SAPI: Error - {e}")
    else:
        print("⚠️ Windows SAPI: Not on Windows")
    
    # Test pyttsx3 (should fail gracefully)
    try:
        import pyttsx3
        engine = pyttsx3.init()
        print("✅ pyttsx3: Available (unexpected)")
        engines_working += 1
    except Exception as e:
        print(f"❌ pyttsx3: Failed as expected - {type(e).__name__}")
    
    print(f"Total working engines: {engines_working}")
    return engines_working > 0

def test_audio_system():
    """Test audio system"""
    print("\n🔊 Testing Audio System...")
    
    try:
        import sounddevice as sd
        
        # Test audio output
        test_tone = 0.1 * np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, 1600))
        sd.play(test_tone, 16000)
        sd.wait()
        
        print("✅ Audio system: Working")
        return True
        
    except Exception as e:
        print(f"❌ Audio system: {e}")
        return False

def test_vad_system():
    """Test VAD system"""
    print("\n🎙️ Testing VAD System...")
    
    try:
        # Test energy-based VAD
        speech_audio = 0.5 * np.random.randn(1600)  # Loud signal
        silence_audio = 0.01 * np.random.randn(1600)  # Quiet signal
        
        def detect_speech(audio_chunk, threshold=0.02):
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            return energy > threshold
        
        speech_detected = detect_speech(speech_audio)
        silence_detected = detect_speech(silence_audio)
        
        print(f"Speech detection: {'✅ PASS' if speech_detected else '❌ FAIL'}")
        print(f"Silence detection: {'✅ PASS' if not silence_detected else '❌ FAIL'}")
        
        return speech_detected and not silence_detected
        
    except Exception as e:
        print(f"❌ VAD test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 VOICE SYSTEM FIXES VERIFICATION")
    print("=" * 50)
    
    results = []
    
    # Test each component
    results.append(("STT Performance", test_stt_performance()))
    results.append(("TTS Engines", test_tts_engines()))
    results.append(("Audio System", test_audio_system()))
    results.append(("VAD System", test_vad_system()))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("🎉 ALL FIXES VERIFIED - SYSTEM READY!")
    else:
        print("⚠️ Some issues remain - check individual test results")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
