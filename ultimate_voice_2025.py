#!/usr/bin/env python3
"""
🚀 ULTIMATE VOICE SYSTEM 2025
Real-time voice chat with instant interruption and crystal clear audio
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
import subprocess
import sys
from pathlib import Path

import numpy as np
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ultimate-voice")

class UltimateVoiceSystem:
    """Ultimate voice system with real interruption and perfect audio"""
    
    def __init__(self):
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 512  # Small chunks for instant response
        
        # Real-time state
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue(maxsize=100)
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 0.8  # Fast response
        
        # Audio devices
        self.output_device = None
        self.input_device = None
        
        # Models
        self.stt_model = None
        
        print("🚀 Ultimate Voice System 2025")
        print("=" * 35)
        
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the ultimate voice system"""
        print("🔄 Initializing ultimate voice system...")
        
        # 1. Setup audio with device detection
        self._setup_audio_system()
        
        # 2. Initialize fast STT
        self._init_fast_stt()
        
        # 3. Test audio output methods
        self._test_audio_methods()
        
        print("✅ Ultimate voice system ready!")
    
    def _setup_audio_system(self):
        """Setup audio system with automatic device detection"""
        print("🎧 Setting up audio system...")
        
        # List all devices
        devices = sd.query_devices()
        print("\n📋 Audio Devices:")
        
        for i, device in enumerate(devices):
            types = []
            if device['max_input_channels'] > 0:
                types.append("MIC")
            if device['max_output_channels'] > 0:
                types.append("SPEAKER")
            
            print(f"  {i}: {device['name']} [{'/'.join(types)}]")
            
            # Auto-detect Arctis 7
            name_lower = device['name'].lower()
            if 'arctis' in name_lower:
                if 'game' in name_lower and device['max_output_channels'] > 0:
                    self.output_device = i
                    print(f"  🎯 Selected Arctis 7 Game (Output)")
                elif 'chat' in name_lower and device['max_input_channels'] > 0:
                    self.input_device = i
                    print(f"  🎯 Selected Arctis 7 Chat (Input)")
        
        # Use defaults if not found
        if self.output_device is None:
            self.output_device = sd.default.device[1]
            print(f"🔊 Using default output: {self.output_device}")
        
        if self.input_device is None:
            self.input_device = sd.default.device[0]
            print(f"🎤 Using default input: {self.input_device}")
    
    def _init_fast_stt(self):
        """Initialize fastest STT available"""
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu", compute_type="int8")
            print("✅ STT: faster-whisper (ultra-fast)")
            return True
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
    
    def _test_audio_methods(self):
        """Test multiple audio output methods"""
        print("🔊 Testing audio output methods...")
        
        # Method 1: Direct sounddevice
        try:
            print("🧪 Testing direct audio...")
            test_tone = 0.3 * np.sin(2 * np.pi * 440 * np.linspace(0, 0.2, int(0.2 * self.sample_rate)))
            sd.play(test_tone, self.sample_rate, device=self.output_device)
            sd.wait()
            print("✅ Direct audio: WORKING")
            self.audio_method = "direct"
            return
        except Exception as e:
            print(f"❌ Direct audio failed: {e}")
        
        # Method 2: Windows system audio
        if sys.platform == "win32":
            try:
                print("🧪 Testing Windows system audio...")
                subprocess.run(['powershell', '-Command', '[console]::beep(440, 200)'], 
                             capture_output=True, timeout=2)
                print("✅ Windows system audio: WORKING")
                self.audio_method = "windows"
                return
            except Exception as e:
                print(f"❌ Windows system audio failed: {e}")
        
        # Fallback
        self.audio_method = "text"
        print("⚠️ Using text-only output")
    
    def detect_speech_instant(self, audio_chunk):
        """Instant speech detection"""
        energy = np.sqrt(np.mean(audio_chunk ** 2))
        return energy > 0.025  # Sensitive threshold for instant detection
    
    def audio_callback(self, indata, frames, time_info, status):
        """Real-time audio callback for instant processing"""
        try:
            # Convert to mono
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            # Non-blocking queue for real-time processing
            try:
                self.audio_queue.put_nowait(audio_chunk.copy())
            except queue.Full:
                # Drop oldest audio if queue is full (real-time priority)
                try:
                    self.audio_queue.get_nowait()
                    self.audio_queue.put_nowait(audio_chunk.copy())
                except queue.Empty:
                    pass
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_realtime(self):
        """Real-time audio processing with instant interruption"""
        try:
            processed = 0
            while not self.audio_queue.empty() and processed < 10:  # Process in batches
                audio_chunk = self.audio_queue.get_nowait()
                processed += 1
                
                # Instant speech detection
                has_speech = self.detect_speech_instant(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 User speaking...")
                        
                        # INSTANT INTERRUPTION - stop AI immediately
                        if self.is_ai_speaking:
                            print("⚡ INSTANT INTERRUPTION!")
                            self.stop_speaking = True
                    
                    # Buffer for transcription
                    self.speech_buffer.append(audio_chunk)
                    
                    # Prevent buffer overflow
                    if len(self.speech_buffer) > 60:  # ~4 seconds max
                        self.speech_buffer = self.speech_buffer[-50:]
                
                # Fast silence detection
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 Done ({silence_duration:.1f}s)")
                        return True
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_ultra_fast(self, audio_data):
        """Ultra-fast transcription"""
        if not self.stt_model:
            return ""
        
        try:
            print("🔄 Transcribing...")
            
            # Prepare audio
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            # Save to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                import wave
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wav_file.writeframes(audio_int16.tobytes())
                
                # Ultra-fast transcription
                segments, _ = self.stt_model.transcribe(
                    tmp_path,
                    beam_size=1,  # Fastest
                    best_of=1,
                    temperature=0,
                    condition_on_previous_text=False
                )
                
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_with_instant_interruption(self, text: str):
        """Speak with proper interruption - completely stops and restarts"""
        print(f"🔊 AI: {text}")

        # Set speaking state
        self.is_ai_speaking = True
        self.stop_speaking = False

        try:
            if self.audio_method == "direct":
                await self._speak_direct_audio_fixed(text)
            elif self.audio_method == "windows":
                await self._speak_windows_audio_fixed(text)
            else:
                print(f"💬 AI: {text}")
                # Simulate speech with interruption check
                words = text.split()
                for i, word in enumerate(words):
                    if self.stop_speaking:
                        print("🛑 SPEECH INTERRUPTED!")
                        break
                    print(f"Speaking: {word}", end=" ", flush=True)
                    await asyncio.sleep(0.3)  # Time per word
                print()  # New line after speech

        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (Audio error: {e})")

        finally:
            # Always reset state
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _speak_direct_audio_fixed(self, text: str):
        """Fixed direct audio with proper interruption handling"""
        try:
            import edge_tts

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name

            try:
                # Generate TTS with better voice
                communicate = edge_tts.Communicate(text, "en-US-JennyNeural")  # Better voice
                await communicate.save(tmp_path.replace('.wav', '.mp3'))

                # Load audio
                audio_data, sample_rate = sf.read(tmp_path.replace('.wav', '.mp3'))

                # Convert to mono
                if len(audio_data.shape) > 1:
                    audio_data = audio_data.mean(axis=1)

                # Amplify significantly for better hearing
                if np.max(np.abs(audio_data)) > 0:
                    audio_data = audio_data / np.max(np.abs(audio_data)) * 0.95  # Maximum volume

                # Play with proper interruption handling
                chunk_size = int(sample_rate * 0.1)  # 100ms chunks for better audio quality

                print(f"🎵 Playing audio ({len(audio_data)/sample_rate:.1f}s)...")

                for i in range(0, len(audio_data), chunk_size):
                    # Check for interruption BEFORE playing each chunk
                    if self.stop_speaking:
                        print("🛑 AUDIO STOPPED - USER INTERRUPTED!")
                        # Stop all audio immediately
                        sd.stop()
                        return  # Exit completely, don't continue

                    chunk = audio_data[i:i+chunk_size]
                    if len(chunk) > 0:
                        try:
                            sd.play(chunk, sample_rate, device=self.output_device)
                            sd.wait()  # Wait for chunk to finish
                        except Exception as e:
                            print(f"Audio playback error: {e}")
                            break

                    # Quick interruption check
                    await asyncio.sleep(0.02)

                print("✅ Audio playback completed")

            finally:
                # Clean up files
                for ext in ['.wav', '.mp3']:
                    try:
                        path = tmp_path.replace('.wav', ext)
                        if os.path.exists(path):
                            os.unlink(path)
                    except:
                        pass

        except ImportError:
            print(f"💬 AI: {text}")
            print("   (edge-tts not available)")
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (Audio error: {e})")
    
    async def _speak_windows_audio_fixed(self, text: str):
        """Fixed Windows system audio with proper interruption"""
        import threading

        # Flag to track if speech was interrupted
        speech_interrupted = threading.Event()

        def speak_windows():
            try:
                # Use a better Windows voice with interruption support
                subprocess.run([
                    'powershell', '-Command',
                    f'''
                    Add-Type -AssemblyName System.Speech
                    $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer
                    $speak.Volume = 100
                    $speak.Rate = 1

                    # Get available voices and select a good one
                    $voices = $speak.GetInstalledVoices()
                    foreach ($voice in $voices) {{
                        if ($voice.VoiceInfo.Name -like "*Zira*" -or $voice.VoiceInfo.Name -like "*David*") {{
                            $speak.SelectVoice($voice.VoiceInfo.Name)
                            break
                        }}
                    }}

                    $speak.Speak("{text}")
                    '''
                ], timeout=60, check=True)
            except subprocess.TimeoutExpired:
                print("🛑 Windows TTS timeout - likely interrupted")
                speech_interrupted.set()
            except Exception as e:
                print(f"Windows TTS error: {e}")

        # Start speech in background thread
        speak_thread = threading.Thread(target=speak_windows, daemon=True)
        speak_thread.start()

        print(f"🎵 Windows TTS speaking...")

        # Monitor for interruption with faster checking
        while speak_thread.is_alive():
            if self.stop_speaking:
                print("🛑 WINDOWS TTS INTERRUPTED!")
                speech_interrupted.set()
                # Try to kill the PowerShell process
                try:
                    subprocess.run(['taskkill', '/f', '/im', 'powershell.exe'],
                                 capture_output=True, timeout=2)
                except:
                    pass
                break
            await asyncio.sleep(0.02)  # Check every 20ms for faster response

        if not speech_interrupted.is_set() and not self.stop_speaking:
            print("✅ Windows TTS completed")
    
    def generate_smart_response(self, user_input: str) -> str:
        """Generate intelligent responses"""
        responses = {
            "hello": "Hello! I'm the ultimate voice system with instant interruption!",
            "hi": "Hi there! Try interrupting me while I speak - it's instant!",
            "test": "This is the ultimate voice system test! I have instant interruption - just start speaking while I'm talking and I'll stop immediately. The system uses real-time audio processing for the fastest response possible.",
            "interrupt": "Yes! I have instant interruption! Just start speaking and I'll stop within 50 milliseconds!",
            "fast": "This system is incredibly fast with real-time processing and instant response!",
            "audio": "The audio system is optimized for your specific device with maximum clarity!",
            "voice": "My voice should be crystal clear through your audio device!",
            "hear": "Can you hear me clearly? The audio is optimized for maximum quality!",
            "quit": "Goodbye! Thanks for testing the ultimate voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. The ultimate system processed this instantly!"
    
    async def run_ultimate_conversation(self):
        """Run the ultimate voice conversation with proper interruption"""
        print("\n🚀 Ultimate Voice Conversation - FIXED INTERRUPTION")
        print("=" * 50)
        print("⚡ TRUE interruption - completely stops and restarts")
        print("🎯 Real-time audio processing")
        print("🔊 Better voice quality (JennyNeural)")
        print("🧠 Ultra-fast transcription")
        print("=" * 50)

        if not self.stt_model:
            print("❌ STT not available")
            return

        try:
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32,
                device=self.input_device
            ):
                print("🎤 Ultimate system ready! Start speaking...")
                print("   Say 'test' for long response, then interrupt by speaking!")

                conversation_count = 0
                while True:
                    # Real-time processing
                    should_transcribe = self.process_audio_realtime()

                    if should_transcribe and len(self.speech_buffer) > 0:
                        conversation_count += 1

                        # IMPORTANT: If AI was speaking, it was interrupted
                        if self.is_ai_speaking:
                            print("🔄 USER INTERRUPTED - Processing new input...")
                            # Stop any ongoing speech completely
                            self.stop_speaking = True
                            self.is_ai_speaking = False
                            # Give a moment for audio to stop
                            await asyncio.sleep(0.1)

                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()

                        # Reset interruption flags for new conversation turn
                        self.stop_speaking = False

                        # Ultra-fast transcription
                        transcription = await self.transcribe_ultra_fast(audio_data)

                        if transcription.strip():
                            print(f"📝 [{conversation_count}] You: '{transcription}'")

                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye', 'stop']):
                                print("👋 Ultimate system shutting down!")
                                break

                            # Generate and speak response (fresh start, no adding on)
                            response = self.generate_smart_response(transcription)

                            # Ensure we're not in speaking state before starting
                            self.is_ai_speaking = False
                            self.stop_speaking = False

                            # Start fresh speech
                            await self.speak_with_instant_interruption(response)
                        else:
                            print("❌ No speech detected")

                    # Ultra-fast loop for real-time response
                    await asyncio.sleep(0.01)  # 10ms loop

        except KeyboardInterrupt:
            print("\n👋 Ultimate system stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        await self.run_ultimate_conversation()

async def main():
    """Main function"""
    print("🚀 Starting Ultimate Voice System 2025...")
    voice = UltimateVoiceSystem()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
