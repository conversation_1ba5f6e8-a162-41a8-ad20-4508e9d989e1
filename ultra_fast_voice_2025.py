#!/usr/bin/env python3
"""
🚀 ULTRA-FAST VOICE SYSTEM 2025
Modern architecture with latest models and instant response
"""

import asyncio
import logging
import os
import tempfile
import time
import threading
import queue
import subprocess
import sys
from pathlib import Path

import numpy as np
import sounddevice as sd
import soundfile as sf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ultra-fast-2025")

class UltraFastVoice2025:
    """Ultra-fast modern voice system"""
    
    def __init__(self):
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 512  # Smaller chunks for faster response
        
        # State management
        self.is_ai_speaking = False
        self.stop_speaking = False
        self.user_speaking = False
        self.audio_queue = queue.Queue(maxsize=100)  # Prevent memory buildup
        self.speech_buffer = []
        self.last_speech_time = 0
        self.silence_threshold = 0.8  # Ultra-fast response
        
        # Models
        self.stt_available = False
        self.tts_available = False
        
        print("🚀 Ultra-Fast Voice System 2025")
        print("=" * 40)
        
        self._init_modern_components()
    
    def _init_modern_components(self):
        """Initialize modern, fast components"""
        print("🔄 Initializing ultra-fast components...")
        
        # 1. Try modern STT options
        self._init_modern_stt()
        
        # 2. Try modern TTS options  
        self._init_modern_tts()
        
        # 3. Modern VAD
        self._init_modern_vad()
        
        print("✅ Modern components initialized!")
    
    def _init_modern_stt(self):
        """Initialize fastest available STT"""
        print("📥 Loading ultra-fast STT...")
        
        # Option 1: Try OpenAI Whisper with optimizations
        try:
            import whisper
            self.stt_model = whisper.load_model("base", device="cpu")
            self.stt_type = "whisper_optimized"
            print("✅ STT: Optimized Whisper loaded")
            self.stt_available = True
            return
        except ImportError:
            pass
        
        # Option 2: Try faster-whisper (already available)
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu", compute_type="int8")
            self.stt_type = "faster_whisper"
            print("✅ STT: faster-whisper (optimized) loaded")
            self.stt_available = True
            return
        except ImportError:
            pass
        
        # Option 3: Try SpeechRecognition with optimizations
        try:
            import speech_recognition as sr
            self.stt_model = sr.Recognizer()
            self.stt_model.energy_threshold = 300
            self.stt_model.dynamic_energy_threshold = True
            self.stt_model.pause_threshold = 0.5
            self.stt_type = "speech_recognition"
            print("✅ STT: SpeechRecognition (optimized) loaded")
            self.stt_available = True
            return
        except ImportError:
            pass
        
        print("❌ No STT available")
    
    def _init_modern_tts(self):
        """Initialize highest quality TTS"""
        print("📥 Loading high-quality TTS...")
        
        # Option 1: Try pyttsx3 for instant local TTS
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Optimize for speed and quality
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to find a good English voice
                for voice in voices:
                    if 'english' in voice.name.lower() or 'aria' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            self.tts_engine.setProperty('rate', 200)  # Faster speech
            self.tts_engine.setProperty('volume', 0.9)
            
            self.tts_type = "pyttsx3"
            print("✅ TTS: pyttsx3 (instant local) loaded")
            self.tts_available = True
            return
        except ImportError:
            pass
        
        # Option 2: Edge-TTS (already available)
        try:
            import edge_tts
            self.tts_type = "edge_tts"
            print("✅ TTS: Edge-TTS (high quality) loaded")
            self.tts_available = True
            return
        except ImportError:
            pass
        
        # Option 3: System TTS fallback
        try:
            if sys.platform == "win32":
                self.tts_type = "system_windows"
                print("✅ TTS: Windows SAPI loaded")
                self.tts_available = True
                return
        except:
            pass
        
        print("❌ No TTS available")
    
    def _init_modern_vad(self):
        """Initialize modern voice activity detection"""
        # Use energy-based VAD with smart thresholds
        self.vad_threshold = 0.02
        self.vad_type = "energy_optimized"
        print("✅ VAD: Optimized energy detection loaded")
    
    def detect_speech_modern(self, audio_chunk):
        """Ultra-fast speech detection"""
        try:
            # Optimized energy detection
            energy = np.sqrt(np.mean(audio_chunk ** 2))
            
            # Adaptive threshold
            if hasattr(self, 'avg_energy'):
                self.avg_energy = 0.95 * self.avg_energy + 0.05 * energy
                threshold = max(self.vad_threshold, self.avg_energy * 2)
            else:
                self.avg_energy = energy
                threshold = self.vad_threshold
            
            return energy > threshold
            
        except Exception:
            return False
    
    def audio_callback(self, indata, frames, time_info, status):
        """Ultra-fast audio processing"""
        try:
            # Convert to mono
            if len(indata.shape) > 1:
                audio_chunk = indata[:, 0]
            else:
                audio_chunk = indata.flatten()
            
            # Non-blocking queue
            try:
                self.audio_queue.put_nowait(audio_chunk.copy())
            except queue.Full:
                # Drop old audio if queue is full
                try:
                    self.audio_queue.get_nowait()
                    self.audio_queue.put_nowait(audio_chunk.copy())
                except queue.Empty:
                    pass
            
        except Exception as e:
            logger.error(f"Audio callback error: {e}")
    
    def process_audio_ultra_fast(self):
        """Ultra-fast audio processing"""
        try:
            processed_chunks = 0
            while not self.audio_queue.empty() and processed_chunks < 10:  # Limit processing
                audio_chunk = self.audio_queue.get_nowait()
                processed_chunks += 1
                
                # Fast speech detection
                has_speech = self.detect_speech_modern(audio_chunk)
                current_time = time.time()
                
                if has_speech:
                    self.last_speech_time = current_time
                    
                    if not self.user_speaking:
                        self.user_speaking = True
                        print("\n🎤 Speaking...")
                        
                        # INSTANT interruption
                        if self.is_ai_speaking:
                            print("⚡ Interrupted!")
                            self.stop_speaking = True
                    
                    # Buffer for transcription
                    self.speech_buffer.append(audio_chunk)
                    
                    # Limit buffer size
                    if len(self.speech_buffer) > 200:  # ~12 seconds max
                        self.speech_buffer = self.speech_buffer[-150:]
                
                # Ultra-fast silence detection
                elif self.user_speaking:
                    silence_duration = current_time - self.last_speech_time
                    if silence_duration > self.silence_threshold:
                        self.user_speaking = False
                        print(f"🔇 Done ({silence_duration:.1f}s)")
                        return True
                        
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
        
        return False
    
    async def transcribe_ultra_fast(self, audio_data):
        """Ultra-fast transcription"""
        try:
            if not self.stt_available:
                return ""
            
            print("🔄 Transcribing...")
            
            # Prepare audio
            if len(audio_data.shape) > 1:
                audio_data = audio_data.mean(axis=1)
            
            if self.stt_type == "faster_whisper":
                # Use existing faster-whisper
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    # Write WAV
                    import wave
                    with wave.open(tmp_path, 'wb') as wav_file:
                        wav_file.setnchannels(1)
                        wav_file.setsampwidth(2)
                        wav_file.setframerate(self.sample_rate)
                        audio_int16 = (audio_data * 32767).astype(np.int16)
                        wav_file.writeframes(audio_int16.tobytes())
                    
                    # Transcribe
                    segments, _ = self.stt_model.transcribe(tmp_path, beam_size=1)  # Faster
                    text = " ".join([segment.text.strip() for segment in segments])
                    return text
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            elif self.stt_type == "whisper_optimized":
                # Direct whisper transcription
                result = self.stt_model.transcribe(audio_data, fp16=False)
                return result["text"].strip()
            
            return ""
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    async def speak_ultra_fast(self, text: str):
        """Ultra-fast, high-quality speech"""
        print(f"🔊 AI: {text}")
        
        self.is_ai_speaking = True
        self.stop_speaking = False
        
        try:
            if self.tts_type == "pyttsx3":
                # Instant local TTS
                def speak_sync():
                    self.tts_engine.say(text)
                    self.tts_engine.runAndWait()
                
                # Run in thread to allow interruption
                speak_thread = threading.Thread(target=speak_sync, daemon=True)
                speak_thread.start()
                
                # Check for interruption
                while speak_thread.is_alive():
                    if self.stop_speaking:
                        print("🛑 Interrupted!")
                        # Stop pyttsx3
                        self.tts_engine.stop()
                        break
                    await asyncio.sleep(0.05)
                
            elif self.tts_type == "edge_tts":
                # High-quality Edge TTS
                import edge_tts
                
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    # Generate audio
                    communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                    await communicate.save(tmp_path)
                    
                    # Load and play in chunks
                    audio_data, sample_rate = sf.read(tmp_path)
                    await self._play_with_interruption(audio_data, sample_rate)
                    
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            elif self.tts_type == "system_windows":
                # Windows SAPI
                def speak_windows():
                    os.system(f'powershell -Command "Add-Type -AssemblyName System.Speech; $speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; $speak.Speak(\'{text}\')"')
                
                speak_thread = threading.Thread(target=speak_windows, daemon=True)
                speak_thread.start()
                
                while speak_thread.is_alive():
                    if self.stop_speaking:
                        print("🛑 Interrupted!")
                        break
                    await asyncio.sleep(0.1)
            
            else:
                # Fallback to text
                print(f"💬 {text}")
                
        except Exception as e:
            print(f"💬 AI: {text}")
            print(f"   (TTS error: {e})")
        
        finally:
            self.is_ai_speaking = False
            self.stop_speaking = False
    
    async def _play_with_interruption(self, audio_data, sample_rate):
        """Play audio with instant interruption"""
        chunk_size = int(sample_rate * 0.05)  # 50ms chunks
        
        for i in range(0, len(audio_data), chunk_size):
            if self.stop_speaking:
                print("🛑 Interrupted!")
                break
            
            chunk = audio_data[i:i+chunk_size]
            sd.play(chunk, sample_rate)
            sd.wait()
            await asyncio.sleep(0.01)
    
    def generate_smart_response(self, user_input: str) -> str:
        """Generate intelligent responses"""
        responses = {
            "hello": "Hello! This is the ultra-fast 2025 voice system with instant response!",
            "hi": "Hi! I'm running on optimized modern architecture for lightning-fast interaction.",
            "test": "Testing the ultra-fast system! I use optimized models and instant interruption. The response time is incredibly fast thanks to modern architecture and efficient processing.",
            "fast": "Yes! This system is designed for maximum speed with minimal latency.",
            "quality": "High quality audio with instant response - the best of both worlds!",
            "interrupt": "Interrupt me anytime! The system detects your voice instantly.",
            "modern": "This is built with 2025 architecture - fast, efficient, and responsive!",
            "quit": "Goodbye! Thanks for trying the ultra-fast voice system!",
        }
        
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        return f"I heard: '{user_input}'. Ultra-fast processing complete!"
    
    async def run_ultra_fast_conversation(self):
        """Run ultra-fast conversation"""
        print("\n🚀 Ultra-Fast Voice Conversation 2025")
        print("=" * 45)
        print("⚡ Instant response time")
        print("🎯 Smart interruption detection") 
        print("🔊 High-quality audio output")
        print("🧠 Modern optimized architecture")
        print("=" * 45)
        
        if not self.stt_available:
            print("⚠️ STT not available - install: pip install faster-whisper")
            return
        
        try:
            with sd.InputStream(
                callback=self.audio_callback,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32
            ):
                print("🎤 Ultra-fast system ready! Start speaking...")
                
                while True:
                    # Ultra-fast processing
                    should_transcribe = self.process_audio_ultra_fast()
                    
                    if should_transcribe and len(self.speech_buffer) > 0:
                        # Combine audio
                        audio_data = np.concatenate(self.speech_buffer)
                        self.speech_buffer.clear()
                        
                        # Ultra-fast transcription
                        transcription = await self.transcribe_ultra_fast(audio_data)
                        
                        if transcription.strip():
                            print(f"📝 You: '{transcription}'")
                            
                            # Check for quit
                            if any(word in transcription.lower() for word in ['quit', 'exit', 'goodbye']):
                                print("👋 Ultra-fast system shutting down!")
                                break
                            
                            # Generate and speak response
                            response = self.generate_smart_response(transcription)
                            await self.speak_ultra_fast(response)
                        else:
                            print("❌ No speech detected")
                    
                    # Ultra-fast loop
                    await asyncio.sleep(0.02)
                    
        except KeyboardInterrupt:
            print("\n👋 Ultra-fast system stopped!")
        except Exception as e:
            print(f"❌ System error: {e}")
    
    async def run(self):
        """Main run function"""
        await self.run_ultra_fast_conversation()

async def main():
    """Main function"""
    print("🚀 Starting Ultra-Fast Voice System 2025...")
    voice = UltraFastVoice2025()
    await voice.run()

if __name__ == "__main__":
    asyncio.run(main())
