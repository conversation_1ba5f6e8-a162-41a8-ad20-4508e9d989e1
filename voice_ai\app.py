#!/usr/bin/env python3
"""voice_ai.app

The core application logic for the voice AI assistant.
"""

import asyncio
import logging
import time
import tempfile
import os
import sys
from pathlib import Path

import numpy as np
from faster_whisper import WhisperModel

from voice_ai.config import settings
from voice_ai.audio import AsyncRecorder, play_audio
from voice_ai.utils import safe_call
from project.ollama_integration import create_ollama_integration, OllamaVoiceIntegration

# Module-level logger
logger = logging.getLogger(__name__)

class VoiceAI:
    """The main Voice AI application class."""

    def __init__(self):
        self.ollama_integration: OllamaVoiceIntegration | None = None
        self.stt_model: WhisperModel | None = None
        self.stop_event = asyncio.Event()  # Global cancellation signal
        self.session_stats = {
            "queries_processed": 0,
            "total_stt_time": 0.0,
            "total_llm_time": 0.0,
            "total_tts_time": 0.0,
            "session_start": time.time(),
        }

    async def initialize(self) -> bool:
        """Initialize all components of the Voice AI."""
        logger.info("🚀 Initializing Voice AI...")

        # Initialize Ollama
        logger.info("🔧 Connecting to Ollama...")
        self.ollama_integration = await create_ollama_integration()
        if not self.ollama_integration:
            logger.error("❌ Failed to initialize Ollama integration")
            return False

        # Initialize STT with GPU auto-detection
        try:
            # Try GPU first, fallback to CPU
            device = "cuda" if self._cuda_available() else "cpu"
            compute_type = "float16" if device == "cuda" else "int8"
            
            logger.info("🔄 Loading faster-whisper model… (%s, %s)", settings.stt.model_size, device)
            self.stt_model = WhisperModel(
                settings.stt.model_size,
                device=device,
                compute_type=compute_type
            )
            logger.info("✅ STT initialized (faster-whisper, %s)", device)
        except Exception as e:
            logger.error("⚠️ STT initialization failed: %s", e)
            return False

        logger.info("✅ Voice AI ready!")
        return True

    def _cuda_available(self) -> bool:
        """Check if CUDA is available for GPU acceleration."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False

    async def record_and_transcribe(self) -> str | None:
        """Record audio and transcribe it to text."""
        try:
            recorder = AsyncRecorder(
                sample_rate=settings.audio.sample_rate,
                chunk_duration=settings.audio.chunk_duration,
                vad_level=settings.audio.vad_level,
                max_silence=settings.audio.max_silence,
            )
            
            # Reset stop event for new recording
            self.stop_event.clear()
            
            audio_data = await recorder.record_until_silence()
            if audio_data is None or audio_data.size == 0:
                logger.info("🤷 No audio recorded.")
                return None

            logger.info("🎤 Recorded %.2fs of audio", len(audio_data) / settings.audio.sample_rate)

            # Transcribe in background thread with proper audio format
            start_time = time.time()
            float_audio = (audio_data.astype('float32') / 32768.0)
            segments, _ = await asyncio.to_thread(
                self.stt_model.transcribe,
                float_audio,
                beam_size=settings.stt.beam_size,
                language=settings.stt.language
            )
            transcription = " ".join([s.text for s in segments]).strip()
            stt_time = time.time() - start_time
            self.session_stats["total_stt_time"] += stt_time
            logger.info("🎯 Transcribed in %.2fs: %s", stt_time, transcription)
            return transcription

        except Exception as e:
            logger.error("❌ Recording/Transcription failed: %s", e)
            return None

    async def generate_and_speak(self, text: str):
        """Generate a response from the LLM and speak it."""
        if not self.ollama_integration:
            return

        logger.info("💭 Processing: %s", text)
        start_time = time.time()

        try:
            # Generate streaming response with cancellation support
            full_response = ""
            async for chunk in self.ollama_integration.generate_response(text, stream=True):
                if self.stop_event.is_set():
                    logger.info("🛑 LLM generation cancelled")
                    return
                full_response += chunk

            llm_time = time.time() - start_time
            self.session_stats["total_llm_time"] += llm_time
            self.session_stats["queries_processed"] += 1
            logger.info("🤖 Generated response in %.2fs", llm_time)

            # Speak the response with cancellation support
            await self.speak_text_streaming(full_response.strip())
            
        except asyncio.CancelledError:
            logger.info("🛑 Generation cancelled")
            raise
        except Exception as e:
            logger.error("❌ Generation error: %s", e)

    async def speak_text_streaming(self, text: str):
        """Synthesize text to speech with streaming and cancellation support."""
        if not text:
            return

        try:
            import edge_tts

            start_time = time.time()
            
            # Create streaming TTS with safe volume settings
            communicate = edge_tts.Communicate(
                text,
                settings.tts.voice,
                rate=settings.tts.rate,
                volume=settings.tts.volume,
            )

            # Stream audio chunks directly to player
            audio_chunks = []
            async for chunk in communicate.stream():
                if self.stop_event.is_set():
                    logger.info("🛑 TTS cancelled")
                    return
                    
                if chunk["type"] == "audio":
                    audio_chunks.append(chunk["data"])

            # Combine chunks and play with volume protection
            if audio_chunks:
                combined_audio = b''.join(audio_chunks)
                
                # Apply volume limiting for ear safety
                combined_audio = self._apply_volume_protection(combined_audio)
                
                # Create temp file for playback (still needed for pygame compatibility)
                fd, wav_path = tempfile.mkstemp(suffix=".wav")
                os.close(fd)
                
                # Write WAV header + safe audio data
                await self._write_wav_file(wav_path, combined_audio)
                
                # Play with cancellation support
                await self._play_with_cancellation(Path(wav_path))

            tts_time = time.time() - start_time
            self.session_stats["total_tts_time"] += tts_time
            logger.info("🔊 Spoke safely in %.2fs", tts_time)

        except asyncio.CancelledError:
            logger.info("🛑 TTS cancelled")
            raise
        except Exception as e:
            logger.error("❌ TTS error: %s", e)
            logger.info("🔊 [TTS failed] AI says: %s", text)

    def _apply_volume_protection(self, audio_data: bytes) -> bytes:
        """Apply volume protection to prevent ear damage."""
        try:
            import numpy as np
            
            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Calculate RMS and apply safe limiting
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            
            if rms > 0:
                # Target safe volume (much quieter)
                target_rms = 0.3 * 32767 * 0.5  # Very conservative
                scale_factor = min(target_rms / rms, 0.6)  # Never exceed 60%
                
                # Apply scaling with soft limiting
                safe_audio = (audio_array.astype(np.float32) * scale_factor).astype(np.int16)
                safe_audio = np.clip(safe_audio, -20000, 20000)  # Additional limiting
                
                logger.debug(f"🔊 Volume protection: RMS {rms:.0f} → {np.sqrt(np.mean(safe_audio.astype(np.float32) ** 2)):.0f}")
                
                return safe_audio.tobytes()
            
            return audio_data
            
        except Exception as e:
            logger.warning(f"⚠️ Volume protection failed: {e}")
            # Fallback: simple attenuation
            try:
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                quiet_audio = (audio_array.astype(np.float32) * 0.3).astype(np.int16)
                return quiet_audio.tobytes()
            except:
                return audio_data

    async def _write_wav_file(self, path: str, audio_data: bytes):
        """Write raw audio data to WAV file."""
        import wave
        
        def write_wav():
            with wave.open(path, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(22050)  # Edge-TTS default
                wav_file.writeframes(audio_data)
        
        await asyncio.to_thread(write_wav)

    async def _play_with_cancellation(self, path: Path):
        """Play audio file with cancellation support."""
        play_task = asyncio.create_task(safe_call(play_audio, path, retries=2))
        
        try:
            # Wait for either playback completion or cancellation
            done, pending = await asyncio.wait(
                [play_task, asyncio.create_task(self.stop_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel remaining tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        except Exception as e:
            logger.error("❌ Playback error: %s", e)

    async def speak_text(self, text: str):
        """Legacy method - redirects to streaming version."""
        await self.speak_text_streaming(text)

    def request_stop(self):
        """Request cancellation of current operations."""
        logger.info("🛑 Stop requested")
        self.stop_event.set()

    async def get_status(self) -> dict:
        """Return a dictionary with the current system status and stats."""
        ollama_status = await self.ollama_integration.get_status() if self.ollama_integration else {}
        return {
            "session_stats": self.session_stats,
            "ollama_status": ollama_status,
            "stop_event_set": self.stop_event.is_set(),
        }

    async def shutdown(self):
        """Clean up resources."""
        logger.info("🔌 Shutting down...")
        self.request_stop()  # Cancel any ongoing operations
        
        if self.ollama_integration:
            await self.ollama_integration.shutdown()
        logger.info("👋 Goodbye!") 