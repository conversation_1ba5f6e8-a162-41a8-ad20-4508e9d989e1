#!/usr/bin/env python3
"""voice_ai.audio.player

Cross-platform, non-blocking audio playback helper.
Optimized for reliability, performance, and user comfort.
"""

from __future__ import annotations

import asyncio
import logging
import sys
import tempfile
import wave
import os
from pathlib import Path
from typing import Union
import platform

import numpy as np

logger = logging.getLogger("voice_ai.audio.player")

# Import detection with priority order
AUDIO_BACKEND = None

try:
    import pygame

    def _ensure_pygame(frequency: int = 16000):
        """Initialise pygame mixer lazily and only once."""
        if not pygame.mixer.get_init():
            pygame.mixer.pre_init(frequency=frequency, size=-16, channels=1, buffer=1024)
            pygame.mixer.init()

    AUDIO_BACKEND = "pygame"
    logger.info("✅ pygame detected (lazy-init mode)")
except ImportError:
    try:
        import simpleaudio as sa
        AUDIO_BACKEND = "simpleaudio"
        logger.info("✅ Using simpleaudio backend")
    except ImportError:
        if sys.platform == "win32":
            import winsound
            AUDIO_BACKEND = "winsound"
            logger.info("✅ Using winsound backend")
        else:
            logger.error("❌ No audio backend available")
            AUDIO_BACKEND = None


def _normalize_audio_volume(audio_data: bytes, target_volume: float = 0.6) -> bytes:
    """Normalize audio volume to prevent ear damage."""
    try:
        # Convert bytes to numpy array
        audio_array = np.frombuffer(audio_data, dtype=np.int16)
        
        # Calculate current RMS volume
        rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
        
        if rms > 0:
            # Calculate scaling factor to reach target volume
            target_rms = target_volume * 32767 * 0.5  # Conservative target
            scale_factor = min(target_rms / rms, 1.0)  # Never amplify above original
            
            # Apply scaling
            normalized = (audio_array.astype(np.float32) * scale_factor).astype(np.int16)
            
            # Apply soft limiting to prevent clipping
            normalized = np.clip(normalized, -32767, 32767)
            
            return normalized.tobytes()
        
        return audio_data
        
    except Exception as e:
        logger.warning(f"⚠️ Audio normalization failed: {e}")
        return audio_data


def _apply_fade(audio_data: bytes, fade_in: float = 0.1, fade_out: float = 0.1, sample_rate: int = 22050) -> bytes:
    """Apply fade in/out to prevent sudden loud sounds."""
    try:
        audio_array = np.frombuffer(audio_data, dtype=np.int16)
        
        fade_in_samples = int(fade_in * sample_rate)
        fade_out_samples = int(fade_out * sample_rate)
        
        # Apply fade in
        if fade_in_samples > 0 and len(audio_array) > fade_in_samples:
            fade_curve = np.linspace(0, 1, fade_in_samples)
            audio_array[:fade_in_samples] = (audio_array[:fade_in_samples].astype(np.float32) * fade_curve).astype(np.int16)
        
        # Apply fade out
        if fade_out_samples > 0 and len(audio_array) > fade_out_samples:
            fade_curve = np.linspace(1, 0, fade_out_samples)
            audio_array[-fade_out_samples:] = (audio_array[-fade_out_samples:].astype(np.float32) * fade_curve).astype(np.int16)
        
        return audio_array.tobytes()
        
    except Exception as e:
        logger.warning(f"⚠️ Audio fade failed: {e}")
        return audio_data


async def play_audio(audio: Union[np.ndarray, bytes, Path, str], sample_rate: int = 16000):
    """Play audio with optimized backend selection and volume protection.
    
    This function never blocks the main event-loop; playback runs in a
    separate thread via ``asyncio.to_thread``.
    """
    if AUDIO_BACKEND is None:
        logger.error("❌ No audio backend available")
        return

    if isinstance(audio, (str, Path)):
        # Play file directly with volume protection
        await asyncio.to_thread(_play_file_safe, Path(audio))
        return

    # Convert data to temporary file with volume protection
    if isinstance(audio, bytes):
        data_bytes = audio
    else:  # numpy array
        data_bytes = (audio * 32767).astype("<i2").tobytes()

    # Apply volume protection
    data_bytes = _normalize_audio_volume(data_bytes, target_volume=0.6)
    data_bytes = _apply_fade(data_bytes, fade_in=0.1, fade_out=0.1, sample_rate=sample_rate)

    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
        with wave.open(tmp, "wb") as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(data_bytes)

        await asyncio.to_thread(_play_file_safe, Path(tmp.name))


def _play_file_safe(path: Path):
    """Safe audio playback with volume protection and error handling."""
    try:
        # Load and process audio file for safety
        with wave.open(str(path), "rb") as wf:
            audio_data = wf.readframes(wf.getnframes())
            sample_rate = wf.getframerate()
            
        # Apply volume protection
        safe_audio = _normalize_audio_volume(audio_data, target_volume=0.6)
        safe_audio = _apply_fade(safe_audio, fade_in=0.1, fade_out=0.1, sample_rate=sample_rate)
        
        # Write safe audio to temp file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            with wave.open(tmp_file, "wb") as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(sample_rate)
                wf.writeframes(safe_audio)
            
            safe_path = Path(tmp_file.name)
        
        # Play the safe audio
        _play_file_optimized(safe_path)
        
        # Cleanup
        safe_path.unlink(missing_ok=True)
        
    except Exception as e:
        logger.error(f"❌ Safe audio playback failed: {e}")
        # Fallback to direct playback with reduced volume
        try:
            _play_file_optimized(path, volume=0.3)
        except Exception:
            pass


def _play_file_optimized(path: Path, volume: float = 1.0):
    """Optimized blocking helper run in a thread."""
    try:
        if AUDIO_BACKEND == "pygame":
            # Pygame - most reliable for WAV/MP3
            _ensure_pygame()
            
            # Load and play with volume control
            sound = pygame.mixer.Sound(str(path))
            sound.set_volume(min(volume * 0.6, 0.6))  # Never exceed 60% volume
            
            channel = sound.play()
            while channel.get_busy():
                pygame.time.wait(50)
            
            # On Windows free the device to avoid handle leaks
            if platform.system() == "Windows":
                pygame.mixer.music.stop()
                pygame.mixer.quit()
                
        elif AUDIO_BACKEND == "simpleaudio":
            # Simpleaudio for WAV files
            if str(path).lower().endswith('.wav'):
                with wave.open(str(path), "rb") as wf:
                    audio_data = wf.readframes(wf.getnframes())
                    
                    # Apply volume reduction
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)
                    audio_array = (audio_array.astype(np.float32) * min(volume * 0.6, 0.6)).astype(np.int16)
                    
                    play_obj = sa.play_buffer(
                        audio_array.tobytes(), 
                        wf.getnchannels(), 
                        wf.getsampwidth(), 
                        wf.getframerate()
                    )
                    play_obj.wait_done()
            else:
                logger.warning("⚠️ Simpleaudio only supports WAV files")
                
        elif AUDIO_BACKEND == "winsound":
            # Windows fallback - no volume control available
            import winsound
            winsound.PlaySound(str(path), winsound.SND_FILENAME | winsound.SND_NODEFAULT)
            
        logger.debug("✅ Audio playback completed safely")
        
    except Exception as e:
        logger.error("❌ Audio playback failed: %s", e)
    finally:
        # Ensure cleanup with retry
        for attempt in range(3):
            try:
                if path.exists():
                    path.unlink()
                break
            except (PermissionError, OSError) as e:
                if attempt < 2:
                    import time
                    time.sleep(0.1)  # Brief delay before retry
                else:
                    logger.warning("⚠️ Could not cleanup temp file: %s", e) 