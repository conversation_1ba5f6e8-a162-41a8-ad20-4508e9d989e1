#!/usr/bin/env python3
"""voice_ai.ui.cli

Command-line interface for the Voice AI assistant with barge-in support.
"""

import asyncio
import logging
import sys
import traceback
from typing import Optional

from voice_ai.app import VoiceAI

logger = logging.getLogger(__name__)

class VoiceCLI:
    """Command-line interface for Voice AI with interruption support."""
    
    def __init__(self):
        self.voice_ai: Optional[VoiceAI] = None
        self.running = True
        
    async def initialize(self) -> bool:
        """Initialize the Voice AI system."""
        try:
            self.voice_ai = VoiceAI()
            return await self.voice_ai.initialize()
        except Exception as e:
            logger.error("❌ Failed to initialize Voice AI: %s", e)
            return False
    
    async def handle_user_input(self) -> Optional[str]:
        """Handle user input with interruption detection."""
        print("\n🎤 Press Enter to speak, or type 'text', 'status', 'stop', or 'quit': ", end="", flush=True)
        
        try:
            # Use asyncio to read input without blocking
            user_input = await asyncio.to_thread(input)
            return user_input.strip().lower()
        except EOFError:
            return "quit"
        except KeyboardInterrupt:
            logger.info("🛑 Keyboard interrupt - requesting stop")
            if self.voice_ai:
                self.voice_ai.request_stop()
            return "stop"

    async def process_voice_interaction(self):
        """Process a voice interaction with barge-in support."""
        if not self.voice_ai:
            print("❌ Voice AI not initialized")
            return
            
        try:
            # Record and transcribe
            print("🎤 Listening... (Press Ctrl+C to interrupt)")
            transcription = await self.voice_ai.record_and_transcribe()
            
            if not transcription:
                print("🤷 No speech detected")
                return
                
            print(f"👤 You said: {transcription}")
            
            # Generate and speak response
            print("🤖 Thinking...")
            await self.voice_ai.generate_and_speak(transcription)
            
        except asyncio.CancelledError:
            print("🛑 Voice interaction cancelled")
        except Exception as e:
            logger.error("❌ Voice interaction failed: %s", e)
            print(f"❌ Error: {e}")

    async def process_text_interaction(self):
        """Process a text-only interaction."""
        if not self.voice_ai:
            print("❌ Voice AI not initialized")
            return
            
        try:
            print("💬 Type your message: ", end="", flush=True)
            text_input = await asyncio.to_thread(input)
            
            if not text_input.strip():
                print("🤷 No text entered")
                return
                
            print(f"👤 You said: {text_input}")
            print("🤖 Thinking...")
            
            # Generate and speak response
            await self.voice_ai.generate_and_speak(text_input.strip())
            
        except EOFError:
            print("🛑 Text input cancelled")
        except Exception as e:
            logger.error("❌ Text interaction failed: %s", e)
            print(f"❌ Error: {e}")

    async def show_status(self):
        """Show system status."""
        if not self.voice_ai:
            print("❌ Voice AI not initialized")
            return
            
        try:
            status = await self.voice_ai.get_status()
            
            print("\n📊 System Status:")
            print("=" * 40)
            
            # Session stats
            stats = status.get("session_stats", {})
            print(f"Queries processed: {stats.get('queries_processed', 0)}")
            print(f"Total STT time: {stats.get('total_stt_time', 0):.2f}s")
            print(f"Total LLM time: {stats.get('total_llm_time', 0):.2f}s")
            print(f"Total TTS time: {stats.get('total_tts_time', 0):.2f}s")
            
            # Ollama status
            ollama = status.get("ollama_status", {})
            print(f"Ollama status: {ollama.get('status', 'unknown')}")
            print(f"Current model: {ollama.get('current_model', 'unknown')}")
            
            # System state
            print(f"Stop event set: {status.get('stop_event_set', False)}")
            print("=" * 40)
            
        except Exception as e:
            logger.error("❌ Failed to get status: %s", e)
            print(f"❌ Status error: {e}")

    async def main_loop(self):
        """Main interaction loop with global exception handling."""
        print("🎯 Voice AI CLI started")
        print("Commands: Enter=voice, 'text'=text mode, 'status'=show status, 'stop'=interrupt, 'quit'=exit")
        
        while self.running:
            try:
                # Handle user input
                user_input = await self.handle_user_input()
                
                if user_input == "quit" or user_input == "exit":
                    print("👋 Goodbye!")
                    break
                elif user_input == "stop":
                    if self.voice_ai:
                        self.voice_ai.request_stop()
                    print("🛑 Operations stopped")
                elif user_input == "status":
                    await self.show_status()
                elif user_input == "text":
                    await self.process_text_interaction()
                elif user_input == "" or user_input == "voice":
                    await self.process_voice_interaction()
                else:
                    print(f"❓ Unknown command: {user_input}")
                    
            except KeyboardInterrupt:
                print("\n🛑 Keyboard interrupt detected")
                if self.voice_ai:
                    self.voice_ai.request_stop()
                print("Type 'quit' to exit or continue...")
                
            except Exception as e:
                logger.error("❌ Unhandled exception in main loop: %s", e)
                print(f"❌ Unexpected error: {e}")
                print("🔄 Restarting main loop...")
                
                # Print traceback for debugging
                if logger.isEnabledFor(logging.DEBUG):
                    traceback.print_exc()
                
                # Try to reset the voice AI state
                if self.voice_ai:
                    try:
                        self.voice_ai.request_stop()
                        await asyncio.sleep(0.5)  # Give time for cleanup
                    except Exception:
                        pass
                
                print("✅ Ready to continue...")

    async def run(self):
        """Run the CLI application."""
        try:
            # Initialize the system
            if not await self.initialize():
                print("❌ Failed to initialize Voice AI. Exiting.")
                return 1
                
            # Run main loop
            await self.main_loop()
            
            # Cleanup
            if self.voice_ai:
                await self.voice_ai.shutdown()
                
            return 0
            
        except Exception as e:
            logger.error("❌ Fatal error in CLI: %s", e)
            print(f"❌ Fatal error: {e}")
            traceback.print_exc()
            return 1

async def main():
    """Entry point for the CLI application."""
    cli = VoiceCLI()
    return await cli.run()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    sys.exit(asyncio.run(main())) 