#!/usr/bin/env python3
"""
🎤 WORKING VOICE CHAT SYSTEM
A simple, functional voice chat system using the optimized components
"""

import asyncio
import logging
import os
import sys
import tempfile
import time
import wave
from pathlib import Path

# Apply voice optimizations
try:
    import project
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("voice-chat")

class WorkingVoiceChat:
    """Simple working voice chat system"""
    
    def __init__(self):
        self.stt_model = None
        self.vad = None
        self.conversation_history = []
        self.is_running = False
        
        print("🎤 Working Voice Chat System")
        print("=" * 40)
        
        self._init_components()
    
    def _init_components(self):
        """Initialize voice components"""
        # Initialize STT
        try:
            from faster_whisper import WhisperModel
            self.stt_model = WhisperModel("tiny.en", device="cpu")
            print("✅ STT: faster-whisper initialized")
        except ImportError:
            print("❌ STT: faster-whisper not available")
            return False
        
        # Initialize VAD
        try:
            import webrtcvad
            self.vad = webrtcvad.Vad(2)
            print("✅ VAD: webrtcvad initialized")
        except ImportError:
            print("❌ VAD: webrtcvad not available")
            return False
        
        # Initialize TTS (optional)
        try:
            import edge_tts
            print("✅ TTS: edge-tts available")
        except ImportError:
            print("⚠️ TTS: edge-tts not available (text-only responses)")
        
        return True
    
    def create_test_audio(self, text: str, duration: float = 3.0) -> bytes:
        """Create synthetic audio for testing"""
        sample_rate = 16000
        samples = int(sample_rate * duration)
        t = np.linspace(0, duration, samples, endpoint=False)
        
        # Create speech-like audio
        freq = 200 + len(text) * 5
        audio = (
            0.3 * np.sin(2 * np.pi * freq * t) +
            0.2 * np.sin(2 * np.pi * freq * 1.5 * t) +
            0.1 * np.sin(2 * np.pi * freq * 2.0 * t)
        )
        
        # Add envelope and noise
        envelope = np.exp(-2 * t / duration)
        noise = 0.05 * np.random.randn(samples)
        audio = (audio + noise) * envelope
        
        # Convert to 16-bit PCM
        audio_int16 = (audio * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    def transcribe_audio(self, audio_data: bytes) -> str:
        """Transcribe audio data to text"""
        if not self.stt_model:
            return ""
        
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                # Write WAV file
                with wave.open(tmp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(16000)
                    wav_file.writeframes(audio_data)
                
                # Transcribe
                segments, _ = self.stt_model.transcribe(tmp_path)
                text = " ".join([segment.text.strip() for segment in segments])
                return text
                
            finally:
                # Clean up
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return ""
    
    def generate_response(self, user_input: str) -> str:
        """Generate a response to user input"""
        # Simple response generation (you can integrate with Ollama here)
        responses = {
            "hello": "Hello! How can I help you today?",
            "how are you": "I'm doing well, thank you for asking!",
            "what is your name": "I'm your voice assistant. What would you like to know?",
            "goodbye": "Goodbye! Have a great day!",
            "test": "Voice system test successful! Everything is working properly.",
            "help": "I can help you with voice conversations. Just speak naturally!",
        }
        
        # Simple keyword matching
        user_lower = user_input.lower().strip()
        for keyword, response in responses.items():
            if keyword in user_lower:
                return response
        
        # Default response
        return f"I heard you say: '{user_input}'. How can I help you with that?"
    
    async def speak_text(self, text: str):
        """Convert text to speech (if available)"""
        try:
            import edge_tts
            
            # Create TTS
            communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                await communicate.save(tmp_path)
                
                # Play audio (simple approach)
                try:
                    import pygame
                    pygame.mixer.init()
                    pygame.mixer.music.load(tmp_path)
                    pygame.mixer.music.play()
                    
                    # Wait for playback to finish
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)
                        
                except ImportError:
                    print(f"🔊 TTS: {text}")
                    
            finally:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except (OSError, PermissionError):
                    pass
                    
        except ImportError:
            # Fallback to text output
            print(f"🔊 Assistant: {text}")
    
    def demo_conversation(self):
        """Run a demo conversation with synthetic audio"""
        print("\n🎤 Demo Conversation Mode")
        print("-" * 40)
        print("This demo simulates voice input with synthetic audio")
        print("In a real system, this would use microphone input")
        print()
        
        demo_inputs = [
            "Hello there",
            "How are you doing today",
            "What is your name",
            "Can you help me with something",
            "Thank you and goodbye"
        ]
        
        for i, demo_text in enumerate(demo_inputs, 1):
            print(f"\n--- Demo Turn {i} ---")
            print(f"🎤 Simulating: '{demo_text}'")
            
            # Create synthetic audio
            audio_data = self.create_test_audio(demo_text)
            
            # Transcribe
            transcribed = self.transcribe_audio(audio_data)
            print(f"📝 Transcribed: '{transcribed}'")
            
            # Generate response
            response = self.generate_response(transcribed)
            print(f"🤖 Response: '{response}'")
            
            # Add to conversation history
            self.conversation_history.append({
                "user": transcribed,
                "assistant": response,
                "timestamp": time.time()
            })
            
            # Simulate TTS (text output)
            print(f"🔊 Speaking: {response}")
            
            # Pause between turns
            time.sleep(2)
        
        print(f"\n✅ Demo completed! {len(self.conversation_history)} exchanges")
    
    def interactive_mode(self):
        """Interactive text-based mode"""
        print("\n💬 Interactive Text Mode")
        print("-" * 40)
        print("Type your messages (or 'quit' to exit)")
        print()
        
        while True:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye!")
                    break
                
                if not user_input:
                    continue
                
                # Generate response
                response = self.generate_response(user_input)
                print(f"Assistant: {response}")
                
                # Add to history
                self.conversation_history.append({
                    "user": user_input,
                    "assistant": response,
                    "timestamp": time.time()
                })
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
    
    def show_menu(self):
        """Show main menu"""
        print("\n🎤 Working Voice Chat System")
        print("=" * 40)
        print("1. Demo Conversation (synthetic audio)")
        print("2. Interactive Text Mode")
        print("3. Show Conversation History")
        print("4. Exit")
        print("-" * 40)
        
        choice = input("Select option (1-4): ").strip()
        return choice
    
    def show_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            print("📝 No conversation history yet")
            return
        
        print(f"\n📝 Conversation History ({len(self.conversation_history)} exchanges)")
        print("-" * 60)
        
        for i, exchange in enumerate(self.conversation_history, 1):
            timestamp = time.strftime("%H:%M:%S", time.localtime(exchange["timestamp"]))
            print(f"{i}. [{timestamp}]")
            print(f"   You: {exchange['user']}")
            print(f"   Assistant: {exchange['assistant']}")
            print()
    
    def run(self):
        """Main run loop"""
        if not self.stt_model or not self.vad:
            print("❌ System not properly initialized")
            return
        
        while True:
            choice = self.show_menu()
            
            if choice == "1":
                self.demo_conversation()
            elif choice == "2":
                self.interactive_mode()
            elif choice == "3":
                self.show_history()
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-4.")
            
            input("\nPress Enter to continue...")

def main():
    """Main function"""
    chat = WorkingVoiceChat()
    chat.run()

if __name__ == "__main__":
    main()
